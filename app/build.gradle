apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'


//配置相应的引入参数
def cfg = rootProject.ext.configuration // 配置
def libs = rootProject.ext.libraries // 库

android {
    compileSdkVersion cfg.compileVersion
    buildToolsVersion cfg.buildToolsVersion
    defaultConfig {
        applicationId String.valueOf(APPPACKNAME)
        minSdkVersion cfg.minSdk
        targetSdkVersion cfg.targetSdk
        versionCode cfg.version_code
        versionName cfg.version_name
        // 控制日志Log 输出打印
        buildConfigField("boolean", "enableLog", "true")
        multiDexEnabled true

        // 客户端通常要 armeabi-v7a 就好
        ndk {
            //abiFilters "armeabi-v7a", "x86"/*, "x86_64"*/
            abiFilters 'x86_64', 'armeabi-v7a', 'arm64-v8a'
        }

    }

    signingConfigs {
        debugconfig {
            keyAlias 'oneMoneyMobile'
            keyPassword 'oneMoneyMobile@one24money20mobile'
            storeFile file('../oneMoneyMobile.jks')
            storePassword 'oneMoneyMobile@one24money20mobile'
        }
    }

    buildTypes {
        release {
            // 控制日志Log 输出打印
            buildConfigField("boolean", "enableLog", "false")
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            signingConfig signingConfigs.debugconfig

            //转换AppType
            resValue('string', 'appType', "${AppType}")
            resValue('string', 'google_map_key', "${GOOGLE_MAP_KEY}")
            //resValue('string', 'jpush_appkey', "${JPUSH_APPKEY}")
        }
        debug {
            // 控制日志Log 输出打印
            buildConfigField("boolean", "enableLog", "false")
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            signingConfig signingConfigs.debugconfig

            //转换AppType
            resValue('string', 'appType', "${AppType}")
            resValue('string', 'google_map_key', "${GOOGLE_MAP_KEY}")
            //resValue('string', 'jpush_appkey', "${JPUSH_APPKEY}")
        }

    }

    bundle{
        language {
            // Specifies that the app bundle should not support
            // configuration APKs for language resources. These
            // resources are instead packaged with each base and
            // feature APK.
            enableSplit = false
        }
    }

    dataBinding {
        enabled true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    android.applicationVariants.all {
        variant ->
            variant.outputs.all {
                //这里修改apk文件名 文件名+ 日期 如果是需要versionCode的话 可以使用defaultConfig
                outputFileName = "oneMoneyMobile_${variant.buildType.name}_" + new Date().format("yyyy-MM-dd") + ".apk"
            }
    }

    packagingOptions {
        exclude 'org/apache/http/version.properties'
        exclude 'org/apache/http/client/version.properties'
    }
    ndkVersion '21.0.6113669'
}



repositories {
    flatDir {
        dirs 'libs', '../Functions/FaceLib/libs'
    }
}

dependencies {

    configurations.all {
        resolutionStrategy {
//            force 'com.github.bumptech.glide:glide:4.11.0'
//            force 'com.github.bumptech.glide:compiler:4.11.0'
            force 'com.scwang.smartrefresh:SmartRefreshLayout:1.0.5.1'
        }
    }

    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "androidx.appcompat:appcompat:${libs.androidx_appcompat}"
    implementation "androidx.constraintlayout:constraintlayout:${libs.androidx_constraintlayout}"
    //基础类库
    api project(path: ':BaseModule:RrtxCommon')
    //登录模块
    implementation project(path: ':Functions:LoginModule')
    //首页模块
    implementation project(path: ':Functions:HomeModule')
    //安全中心模块
    implementation project(path: ':Functions:SecurityModule')
    //转账模块
    implementation project(path: ':Functions:TransferModule')
    //充值模块
    implementation project(path: ':Functions:TopUpModule')
    //收付款模块
    implementation project(path: ':Functions:PaymentModule')
    //收银台模块
    implementation project(path: ':Functions:CashierModel')
    //推广模块
    implementation project(path: ':Functions:PromotionModel')
    //推送模块
    //implementation project(path: ':Functions:JPushModel')
    //Google推送模块
//    implementation project(path: ':Functions:GPushModule')
    //外部支付模块
    implementation project(path: ':Functions:XStoreLib')
    //地图模块
    implementation project(path: ':BaseModule:GoogleMapModule')
    //人脸模块
    //implementation project(path: ':Functions:FaceLib')
    //分包
    implementation "com.android.support:multidex:${libs.multidex}"

    implementation 'com.google.firebase:firebase-analytics:21.3.0'
    implementation 'com.google.firebase:firebase-messaging:23.2.0'
}
