package zw.co.onemoney.mob;

public class Constants {

    public interface Urls {
          String BASE_TEST_URL = "https://msf-xwallet-user.rrtx.vimbug.com";//测试环境
        //String BASE_TEST_URL = "http://dk2.proxy.vimbug.com";//马德环境
        //String BASE_TEST_URL = "https://digitalbank-mb-app-guozp.proxy.vimbug.com";//志鹏环境
        //String BASE_TEST_URL = "https://whr-wallet.proxy.vimbug.com/";//惠如环境
//        String BASE_TEST_URL = "http://zhangjh.proxy.vimbug.com";//军辉环境
        //String BASE_TEST_URL = "https://xwallet.proxy.vimbug.com/";//张洁环境
//        String BASE_TEST_URL = "http://d69byx.natappfree.cc";//文强环境
//        String BASE_TEST_URL = "http://xingcs.proxy.vimbug.com/";//成思
//        String BASE_TEST_URL = "http://a-xwallet-yy.proxy.vimbug.com/";//岳阳
//        String BASE_TEST_URL = "https://xuxs-merchant.proxy.vimbug.com/";//先山
        String BASE_DEV_URL = "https://xwallet-user-dev.rrtx.vimbug.com/";//开发环境
        String BASE_UAT_URL = "https://wallet-server.onemoney.co.zw/";//演示环境
        String BASE_FAT_URL = "https://mfs-xwallet-server-fat.rrtx.vimbug.com/";//验收环境
    }

    public interface URL {
        String CASHLOG = "/appCrashLog/save";
    }

    /**
     * 传递参数索引
     */
    public interface Parameter {
        /**
         * 时间戳
         */
        String TIMESTAMP = "timestamp";
        /**
         * 接口随机数
         */
        String RANDOM = "random";
        /**
         * 设备标识
         */
        String DEVICEOS = "deviceOS";
        /**
         * 请求方式
         */
        String SIGNMETHOD = "signMethod";
        /**
         * 平台
         */
        String PLATFORM = "platform";
        /**
         * 版本号
         */
        String APPVERSION = "appVersion";
        /**
         * 手机品牌
         */
        String BRAND = "brand";
        /**
         * 手机系列
         */
        String SERIES = "series";
        /**
         * 系统版本
         */
        String SYSTEMVERSION = "systemVersion";
        /**
         * 语言
         */
        String LANGUAGE = "language";
        /**
         * 设备唯一标识
         */
        String SINGLECODE = "singleCode";
        /**
         * app名称
         */
        String APPNAME = "appName";
        /**
         * 用户id
         */
        String USERID = "userId";
        /**
         * 加密秘钥
         */
        String ENCRYPTKEY = "encryptKey";
        /**
         * 加密内容
         */
        String ENCRYPTDATA = "encryptData";
        /**
         * 加密标识
         */
        String SIGNDATA = "signData";
        /**
         * 异常信息
         */
        String CRASHREASON = "crashReason";
    }
}
