package zw.co.onemoney.mob.net;

import static com.squareup.okhttp.internal.Util.UTF_8;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import androidx.annotation.RequiresApi;

import zw.co.onemoney.mob.Constants;

import org.json.JSONObject;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.security.PublicKey;
import java.util.HashMap;
import java.util.Map;

import cn.hutool.core.util.RandomUtil;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.net.PostJsonBody;
import om.rrtx.mobile.rrtxcommon1.utils.AppInfoUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CryptoUtil;
import om.rrtx.mobile.rrtxcommon1.utils.DeviceUtils;
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager;
import om.rrtx.mobile.rrtxcommon1.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.utils.RSAUtils;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.safety.DateEncryption;

/**
 * <AUTHOR> Angle
 * 创建时间 : 2019/1/7 11:34
 * 描述 : 准备在这里解决请求和相应加密的问题
 * 这个只是针对POST请求进行处理的，如果要是GET请求参见下面的两篇文章
 */
public class PBDataEncryptInterceptor implements Interceptor {
    private static final String POST = "POST";
    private static final String GET = "GET";
    private static final String ENCODE = "utf-8";
    private static final String TAGFLAG = "while(1);";
    private static final int CODE_OK = 200;
    private static String sSignMethod;
    private Context mContext;
    private MediaType mediaType;

    public PBDataEncryptInterceptor(Context context, String signMethod) {
        sSignMethod = signMethod;
        mContext = context;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    public Response intercept(Chain chain) throws IOException {

        Request request = chain.request();
        LogUtil.e("TAG", "intercept: " + request.method());
        if (POST.equals(request.method())) {
            //https://www.jishux.com/p/837a3df82fc47f62
            //https://blog.csdn.net/wuyinlei/article/details/57087872

            RequestBody requestBody = request.body();
            if (requestBody instanceof PostJsonBody) {
                Map<String, Object> hashMap = new HashMap<>();

                //添加时间戳
                hashMap.put(Constants.Parameter.TIMESTAMP,
                        URLDecoder.decode(String.valueOf(System.currentTimeMillis()), ENCODE));
                //时间戳
                hashMap.put(Constants.Parameter.RANDOM, URLDecoder.decode(DeviceUtils.getUniqueId(mContext) + System.currentTimeMillis(), ENCODE));
                //设备标识
                hashMap.put(Constants.Parameter.DEVICEOS,
                        URLDecoder.decode("1", ENCODE));
                //加密算法
                hashMap.put(Constants.Parameter.SIGNMETHOD,
                        URLDecoder.decode(sSignMethod, ENCODE));
                //platform 平台 iOS Android
                hashMap.put(Constants.Parameter.PLATFORM, URLDecoder.decode("1", ENCODE));
                //appVersion APP版本号
                hashMap.put(Constants.Parameter.APPVERSION,
                        URLDecoder.decode(String.valueOf(AppInfoUtils.getVersionCode(mContext)), ENCODE));
                //brand 手机品牌
                hashMap.put(Constants.Parameter.BRAND,
                        URLDecoder.decode(AppInfoUtils.getDeviceBrand(), ENCODE));
                //series 手机系列
                hashMap.put(Constants.Parameter.SERIES,
                        URLDecoder.decode(AppInfoUtils.getSystemModel(), ENCODE));
                //systemVersion 手机系统版本
                hashMap.put(Constants.Parameter.SYSTEMVERSION,
                        URLDecoder.decode(AppInfoUtils.getSystemVersion(), ENCODE));
                //app类型
                hashMap.put(Constants.Parameter.APPNAME,
                        URLDecoder.decode("0", ENCODE));

                //language 当前语言
                String currentLocale = LocaleManager.getInstance().getLanguage(mContext);
                if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_CHINA)) {
                    //language 当前语言
                    hashMap.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("zh", ENCODE));
                } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_NDEBELE)) {
                    //language 当前语言
                    hashMap.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("nr", ENCODE));
                } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_SHONA)) {
                    //language 当前语言
                    hashMap.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("sn", ENCODE));
                } else {
                    hashMap.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("en", ENCODE));
                }

                //singleCode
                hashMap.put(Constants.Parameter.SINGLECODE,
                        URLDecoder.decode(DeviceUtils.getUniqueId(mContext), ENCODE));

                String content = ((PostJsonBody) requestBody).getContent();
                String pubStr = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
                if (!TextUtils.isEmpty(content) && !TextUtils.isEmpty(pubStr)) {
                    LogUtil.e("TAG", "PostJsonBody 有pub");
                    try {
                        JSONObject requestJson = new JSONObject(content);
                        PublicKey publicKey = RSAUtils.getPublicKey(pubStr);
                        String currentRandom = RandomUtil.randomString(16);
                        String encryptKey = RSAUtils.specialEncrypt(currentRandom, publicKey);
                        LogUtil.e("TAG", "解密: " + new String(Base64.decode(encryptKey.getBytes("utf-8"), Base64.NO_WRAP)));
                        String encryptData = CryptoUtil.aesEncrypt(requestJson.toString(), currentRandom);
                        String signData = CryptoUtil.signWithSha256(requestJson.toString());
                        hashMap.put(Constants.Parameter.ENCRYPTKEY, encryptKey);
                        hashMap.put(Constants.Parameter.ENCRYPTDATA, encryptData);
                        hashMap.put(Constants.Parameter.SIGNDATA, signData);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //这里获取的集和就是原始的数据了,因为这里上送的是json所以需要这么写
                    requestBody = FormBody.create(MediaType.parse("application/json; charset=utf-8"), new JSONObject(hashMap).toString());
                } else {
                    LogUtil.e("TAG", "PostJsonBody 无pub");
                    //这里获取的集和就是原始的数据了,因为这里上送的是json所以需要这么写
                    String encode = DateEncryption.encode(hashMap);
                    requestBody = FormBody.create(MediaType.parse("application/json; charset=utf-8"), encode);
                }
                Log.e("TAG", "params: " + hashMap.toString());
                Request.Builder builder = request.newBuilder();


                //token设置给Header
                String authorization = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, "");
                LogUtil.e("TAG", "intercept: token  PostJsonBody" + authorization);
                if (!TextUtils.isEmpty(authorization)) {
                    builder.addHeader(BaseConstants.SaveParameter.AUTHORIZATION, authorization);
                }
                //安全token设置个header
                String securityToken = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.SECURITYTOKEN, "");
                if (!TextUtils.isEmpty(securityToken)) {
                    builder.addHeader(BaseConstants.SaveParameter.SECURITYTOKEN, securityToken);
                }
                request = builder.post(requestBody).build();
            } else if (requestBody instanceof FormBody) {
                Map<String, Object> map = new HashMap<>(16);

                FormBody formBody = (FormBody) request.body();

                //添加时间戳
                map.put(Constants.Parameter.TIMESTAMP,
                        URLDecoder.decode(String.valueOf(System.currentTimeMillis()), ENCODE));
                //添加随机数
                map.put(Constants.Parameter.RANDOM, URLDecoder.decode(DeviceUtils.getUniqueId(mContext) + System.currentTimeMillis(), ENCODE));
                LogUtil.e("唯一标识", "intercept: " + DeviceUtils.getUniqueId(mContext));
                //设备标识
                map.put(Constants.Parameter.DEVICEOS,
                        URLDecoder.decode("1", ENCODE));
                //加密算法
                map.put(Constants.Parameter.SIGNMETHOD,
                        URLDecoder.decode(sSignMethod, ENCODE));
                //platform 平台 iOS Android
                map.put(Constants.Parameter.PLATFORM, URLDecoder.decode("1", ENCODE));
                //appVersion APP版本号
                map.put(Constants.Parameter.APPVERSION,
                        URLDecoder.decode(String.valueOf(AppInfoUtils.getVersionCode(mContext)), ENCODE));
                //brand 手机品牌
                map.put(Constants.Parameter.BRAND,
                        URLDecoder.decode(AppInfoUtils.getDeviceBrand(), ENCODE));
                //series 手机系列
                map.put(Constants.Parameter.SERIES,
                        URLDecoder.decode(AppInfoUtils.getSystemModel(), ENCODE));
                //systemVersion 手机系统版本
                map.put(Constants.Parameter.SYSTEMVERSION,
                        URLDecoder.decode(AppInfoUtils.getSystemVersion(), ENCODE));
                //app类型
                map.put(Constants.Parameter.APPNAME,
                        URLDecoder.decode("0", ENCODE));

                //language 当前语言
                String currentLocale = LocaleManager.getInstance().getLanguage(mContext);
                if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_CHINA)) {
                    //language 当前语言
                    map.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("zh", ENCODE));
                } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_SHONA)) {
                    //language 当前语言
                    map.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("sn", ENCODE));
                } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_NDEBELE)) {
                    //language 当前语言
                    map.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("nr", ENCODE));
                } else {
                    map.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("en", ENCODE));
                }

                //singleCode
                map.put(Constants.Parameter.SINGLECODE,
                        URLDecoder.decode(DeviceUtils.getUniqueId(mContext), ENCODE));

                JSONObject requestJson = new JSONObject();
                String pubStr = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
                LogUtil.e("TAG", "intercept: pubStr=== " + pubStr);
                if (formBody != null && !TextUtils.isEmpty(pubStr)) {
                    LogUtil.e("TAG", "FormBody 有pub");
                    try {
                        for (int i = 0; i < formBody.size(); i++) {
                            requestJson.put(formBody.encodedName(i), URLDecoder.decode(formBody.encodedValue(i), ENCODE));
                        }
                        LogUtil.e("TAG", "请求json: " + requestJson);
                        String currentRandom = RandomUtil.randomString(16);
                        PublicKey publicKey = RSAUtils.getPublicKey(pubStr);
                        String encryptKey = RSAUtils.specialEncrypt(currentRandom, publicKey);
                        LogUtil.e("TAG", "intercept: " + encryptKey);
                        LogUtil.e("TAG", "解密: " + new String(Base64.decode(encryptKey.getBytes("UTF-8"), Base64.DEFAULT)));
                        String encryptData = CryptoUtil.aesEncrypt(requestJson.toString(), currentRandom);
                        LogUtil.e("aes", "intercept: " + encryptData);
                        String signData = CryptoUtil.signWithSha256(requestJson.toString());
                        map.put(Constants.Parameter.ENCRYPTKEY, encryptKey);
                        map.put(Constants.Parameter.ENCRYPTDATA, encryptData);
                        map.put(Constants.Parameter.SIGNDATA, signData);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //这里获取的集和就是原始的数据了,因为这里上送的是json所以需要这么写
                    requestBody = FormBody.create(MediaType.parse("application/json; charset=utf-8"), new JSONObject(map).toString());
                } else {
                    LogUtil.e("TAG", "FormBody 无pub");
                    //这里获取的集和就是原始的数据了,因为这里上送的是json所以需要这么写
                    String encode = DateEncryption.encode(map);
                    requestBody = FormBody.create(MediaType.parse("application/json; charset=utf-8"), encode);
                }
//                LogUtil.e("TAG", "最终报文>>> " + map.toString());
                Log.e("TAG", "params: " + map.toString());
                Request.Builder builder = request.newBuilder();

                //token设置给Header
                String authorization = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, "");
                LogUtil.e("TAG", "intercept: token FormBody " + authorization);

                if (!TextUtils.isEmpty(authorization)) {
                    builder.addHeader(BaseConstants.SaveParameter.AUTHORIZATION, authorization);
                }
                //安全token设置个header
                String securityToken = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.SECURITYTOKEN, "");
                if (!TextUtils.isEmpty(securityToken)) {
                    builder.addHeader(BaseConstants.SaveParameter.SECURITYTOKEN, securityToken);
                }
                request = builder.post(requestBody).build();
            } else if (requestBody instanceof MultipartBody) {

            } else {
                Map<String, Object> map = new HashMap<>(16);

                //添加时间戳
                map.put(Constants.Parameter.TIMESTAMP,
                        URLDecoder.decode(String.valueOf(System.currentTimeMillis()), ENCODE));
                //添加随机数
                map.put(Constants.Parameter.RANDOM, URLDecoder.decode(DeviceUtils.getUniqueId(mContext) + System.currentTimeMillis(), ENCODE));
                LogUtil.e("唯一标识", "intercept: " + DeviceUtils.getUniqueId(mContext));
                //设备标识
                map.put(Constants.Parameter.DEVICEOS,
                        URLDecoder.decode("1", ENCODE));
                //加密算法
                map.put(Constants.Parameter.SIGNMETHOD,
                        URLDecoder.decode(sSignMethod, ENCODE));
                //platform 平台 iOS Android
                map.put(Constants.Parameter.PLATFORM, URLDecoder.decode("1", ENCODE));
                //appVersion APP版本号
                map.put(Constants.Parameter.APPVERSION,
                        URLDecoder.decode(String.valueOf(AppInfoUtils.getVersionCode(mContext)), ENCODE));
                //brand 手机品牌
                map.put(Constants.Parameter.BRAND,
                        URLDecoder.decode(AppInfoUtils.getDeviceBrand(), ENCODE));
                //series 手机系列
                map.put(Constants.Parameter.SERIES,
                        URLDecoder.decode(AppInfoUtils.getSystemModel(), ENCODE));
                //systemVersion 手机系统版本
                map.put(Constants.Parameter.SYSTEMVERSION,
                        URLDecoder.decode(AppInfoUtils.getSystemVersion(), ENCODE));
                //app类型
                map.put(Constants.Parameter.APPNAME,
                        URLDecoder.decode("0", ENCODE));

                //language 当前语言
                String currentLocale = LocaleManager.getInstance().getLanguage(mContext);
                if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_CHINA)) {
                    //language 当前语言
                    map.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("zh", ENCODE));
                } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_SHONA)) {
                    //language 当前语言
                    map.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("sn", ENCODE));
                } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_NDEBELE)) {
                    //language 当前语言
                    map.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("nr", ENCODE));
                } else {
                    map.put(Constants.Parameter.LANGUAGE, URLDecoder.decode("en", ENCODE));
                }

                //singleCode
                map.put(Constants.Parameter.SINGLECODE,
                        URLDecoder.decode(DeviceUtils.getUniqueId(mContext), ENCODE));

//                JSONObject requestJson = new JSONObject();
                Buffer buffer = new Buffer();
                requestBody.writeTo(buffer);
                Charset charset = UTF_8;
                String json = buffer.readString(charset);
                Log.e("TAG", "请求json ----> " + json);

                String pubStr = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
                if (!TextUtils.isEmpty(json) && !TextUtils.isEmpty(pubStr)) {
                    LogUtil.e("TAG", "FormBody 有pub");
                    try {
//                        for (int i = 0; i < formBody.size(); i++) {
//                            requestJson.put(formBody.encodedName(i), URLDecoder.decode(formBody.encodedValue(i), ENCODE));
//                        }
//                        LogUtil.e("TAG", "请求json: " + requestJson);
                        String currentRandom = RandomUtil.randomString(16);
                        PublicKey publicKey = RSAUtils.getPublicKey(pubStr);
                        String encryptKey = RSAUtils.specialEncrypt(currentRandom, publicKey);
                        LogUtil.e("TAG", "intercept: " + encryptKey);
                        LogUtil.e("TAG", "解密: " + new String(Base64.decode(encryptKey.getBytes("UTF-8"), Base64.DEFAULT)));
//                        String encryptData = CryptoUtil.aesEncrypt(requestJson.toString(), currentRandom);
                        String encryptData = CryptoUtil.aesEncrypt(json, currentRandom);
                        Log.e("aes", "intercept: " + encryptData);
//                        String signData = CryptoUtil.signWithSha256(requestJson.toString());
                        String signData = CryptoUtil.signWithSha256(json);
                        map.put(Constants.Parameter.ENCRYPTKEY, encryptKey);
                        map.put(Constants.Parameter.ENCRYPTDATA, encryptData);
                        map.put(Constants.Parameter.SIGNDATA, signData);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //这里获取的集和就是原始的数据了,因为这里上送的是json所以需要这么写
                    requestBody = FormBody.create(MediaType.parse("application/json; charset=utf-8"), new JSONObject(map).toString());
                } else {
                    LogUtil.e("TAG", "FormBody 无pub");
                    //这里获取的集和就是原始的数据了,因为这里上送的是json所以需要这么写
                    String encode = DateEncryption.encode(map);
                    requestBody = FormBody.create(MediaType.parse("application/json; charset=utf-8"), encode);
                }

                Request.Builder builder = request.newBuilder();

                //token设置给Header
                String authorization = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, "");
                LogUtil.e("TAG", "intercept: token FormBody " + authorization);

                if (!TextUtils.isEmpty(authorization)) {
                    builder.addHeader(BaseConstants.SaveParameter.AUTHORIZATION, authorization);
                }
                //安全token设置个header
                String securityToken = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.SECURITYTOKEN, "");
                if (!TextUtils.isEmpty(securityToken)) {
                    builder.addHeader(BaseConstants.SaveParameter.SECURITYTOKEN, securityToken);
                }
                request = builder.post(requestBody).build();
            }
        }

        MediaType mediaType = MediaType.parse("text/plain; charset=utf-8");
        //响应
        Response response = chain.proceed(request);

        if (response.code() == CODE_OK) {
            Headers headers = response.headers();
            //保存登录token
            String authorization = headers.get(BaseConstants.SaveParameter.AUTHORIZATION);
            if (!TextUtils.isEmpty(authorization)) {
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, authorization);
            }
            //获取校验token
            String securityToken = headers.get(BaseConstants.SaveParameter.SECURITYTOKEN);
            if (!TextUtils.isEmpty(securityToken)) {
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.SECURITYTOKEN, securityToken);
            }
            ResponseBody oldResponseBody = response.body();
            if (oldResponseBody != null) {
                String pubStr = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
                String oldResponseBodyStr = oldResponseBody.string();
                if (!TextUtils.isEmpty(pubStr)) {
                    LogUtil.e("TAG", "响应有pub");
                    try {
                        JSONObject oldJson = new JSONObject(oldResponseBodyStr);
                        String newResponseBodyStr = null;
                        if (TextUtils.isEmpty(oldJson.optString("encryptKey"))) {
                            newResponseBodyStr = oldResponseBodyStr;
                        } else {
                            //rsa解密
                            PublicKey publicKey = RSAUtils.getPublicKey(pubStr);
                            String encryptKey = CryptoUtil.rsaDecrypt(oldJson.optString("encryptKey"), publicKey);
                            LogUtil.e("TAG", "解密的key: " + encryptKey);
                            //aes解密
                            String data = CryptoUtil.aesDecrypt(oldJson.optString("data"), encryptKey);
                            LogUtil.e("TAG", "intercept: " + data);
                            if (CryptoUtil.verifyWithSha256(data, oldJson.optString("signData"))) {
                                //验签通过了
                                oldJson.putOpt("data", new JSONObject(data));
                                newResponseBodyStr = oldJson.toString();
                                LogUtil.e("TAG", "intercept: " + newResponseBodyStr);
                            }
                        }
                        oldResponseBody.close();
                        //构造新的response
                        ResponseBody newResponseBody = ResponseBody.create(mediaType, newResponseBodyStr);
                        response = response.newBuilder().body(newResponseBody).build();
                        newResponseBody.close();
                    } catch (Exception e) {
                        LogUtil.e("TAG", "接口返回值处理失败："+e.toString());
//                        e.printStackTrace();
                    }
                } else {
                    LogUtil.e("TAG", "响应没有pub");
                    String newResponseBodyStr;
                    if (oldResponseBodyStr.contains(TAGFLAG)) {
                        newResponseBodyStr = oldResponseBodyStr.replace(TAGFLAG, "");
                    } else {
                        newResponseBodyStr = oldResponseBodyStr;
                    }
                    LogUtil.e("请求数据--->", "intercept: " + newResponseBodyStr);
                    oldResponseBody.close();
                    //构造新的response
                    ResponseBody newResponseBody = ResponseBody.create(mediaType, newResponseBodyStr);
                    response = response.newBuilder().body(newResponseBody).build();
                    newResponseBody.close();
                }
            }
        }
        return response;
    }
}
