package zw.co.onemoney.mob;

import android.content.Context;


public class AppInfo {
    private boolean mIsXposedInstall;
    private String mXposedVersionName;

    private static volatile AppInfo mInstance = null;

    private AppInfo() {

    }

    public static AppInfo getInstance() {
        if (mInstance == null) {
            synchronized (AppInfo.class) {
                if (mInstance == null) {
                    mInstance = new AppInfo();
                }
            }
        }
        return mInstance;
    }

    public void ValidateEnvironment(Context context) {
        mXposedVersionName = AppUtil.getAppVersionName(context, AppUtil.PACKAGE_NAME_XPOSED);
        mIsXposedInstall = !AppUtil.isEmpty(getmXposedVersionName());
    }

    public boolean isXposedActive() {
        return AppUtil.isModuleActive();
    }

    public boolean ismIsXposedInstall() {
        return mIsXposedInstall;
    }

    public void setmIsXposedInstall(boolean xposedInstall) {
        mIsXposedInstall = xposedInstall;
    }

    public String getmXposedVersionName() {
        return mXposedVersionName;
    }

    public void setmXposedVersionName(String xposedVersionName) {
        mXposedVersionName = xposedVersionName;
    }
}
