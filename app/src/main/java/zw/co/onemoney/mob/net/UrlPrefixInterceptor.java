package zw.co.onemoney.mob.net;

import android.util.Log;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class UrlPrefixInterceptor implements Interceptor {
    private String prefix;

    public UrlPrefixInterceptor(String prefix) {
        this.prefix = prefix;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        // 获取原始请求
        Request originalRequest = chain.request();

        // 获取原始 URL
        String originalUrl = originalRequest.url().toString();
        Log.e("======url=",originalUrl);
        // 分割 URL 为域名和路径
        int pathStartIndex = originalUrl.indexOf('/', "https://".length());
        String domain = originalUrl.substring(0, pathStartIndex);
        String path = originalUrl.substring(pathStartIndex);

        // 检查路径是否已经包含前缀，避免重复添加
        if (!path.startsWith(prefix)) {
            // 添加前缀
            String prefixedPath = prefix + path;

            // 构建新的 URL
            String newUrl = domain + prefixedPath;

            // 构建新的请求
            Request newRequest = originalRequest.newBuilder()
                    .url(newUrl)
                    .build();

            // 继续链式调用
            return chain.proceed(newRequest);
        } else {
            // 如果已经包含前缀，直接返回原始请求
            return chain.proceed(originalRequest);
        }
    }
}
