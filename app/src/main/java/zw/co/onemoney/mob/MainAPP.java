package zw.co.onemoney.mob;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.multidex.MultiDex;

import com.alibaba.android.arouter.launcher.ARouter;
import com.bumptech.glide.Glide;
import zw.co.onemoney.mob.net.HttpLogger;
import zw.co.onemoney.mob.net.PBDataEncryptInterceptor;
import com.franmontiel.persistentcookiejar.PersistentCookieJar;
import com.franmontiel.persistentcookiejar.cache.SetCookieCache;
import com.franmontiel.persistentcookiejar.persistence.SharedPrefsCookiePersistor;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.messaging.FirebaseMessaging;
import com.kapp.xmarketing.CouponInfoConfig;
import com.kapp.xmarketing.CouponInfoImpl;
import com.kapp.xmarketing.ShowMarketingManager;

import com.rrtx.googlemapmodule.GoogleMapManager;
import com.rrtx.xstorelib.service.XStoreLibServiceImpl;

import org.json.JSONObject;

import okhttp3.Interceptor;
import okhttp3.logging.HttpLoggingInterceptor;
import om.rrtx.mobile.functionapi.ServiceFactory;
import om.rrtx.mobile.paymentmodule.MyPaymentService;
import om.rrtx.mobile.paymentmodule.activity.MakePaymentAllActivity;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.image.GlideImageLoader;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitConfig;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitServiceManager;
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager;
import om.rrtx.mobile.rrtxcommon1.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.topupmodule.MyTopUpService;
import om.rrtx.mobile.transfermodule.MyTransferService;

/**
 * 主项目的APP类
 */
public class MainAPP extends BaseApp{

    @Override
    public void onCreate() {
        super.onCreate();
        try {
            Thread.setDefaultUncaughtExceptionHandler(new CustomExceptionHandler(this));

            LocaleManager.getInstance()
                    .setNewLocale(getAPPContext(), LocaleManager.getInstance().getLanguage(getAPPContext()));
            initNetWork();
            initARouter();
            //initImageLoader();
            //设置对外暴露的内容
            ServiceFactory.getInstance().setPaymentService(new MyPaymentService());
            ServiceFactory.getInstance().setTransferService(new MyTransferService());
            ServiceFactory.getInstance().setTopUpService(new MyTopUpService());
            ServiceFactory.getInstance().setXStoreLibService(new XStoreLibServiceImpl());
            //初始化Google推送
            initGpush();
            //地图的初始化
            GoogleMapManager.getInstance().initMap(this);
            initMarketing();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void initMarketing() {
        try {
            ShowMarketingManager.getInstance().initCouponInfo(new CouponInfoConfig.Builder()
                    .setContext(this)
                    .setCouponInfo(new CouponInfoImpl() {
                        @Override
                        public void goPaymentCode(Context context, JSONObject jsonObject) {
                            Intent intent = new Intent(context, MakePaymentAllActivity.class);
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                            startActivity(intent);
                        }

                        @Override
                        public void goPaymentCode(Context context) {
                            Intent intent = new Intent(context, MakePaymentAllActivity.class);
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            startActivity(intent);
                        }

                        @Override
                        public void goLuckyDraw(Context context, JSONObject jsonObject) {

                        }

                        @Override
                        public void goLuckyDrawCam(Context context, JSONObject jsonObject) {

                        }

                        @Override
                        public void goWinRecord(Context context, JSONObject jsonObject) {

                        }

                        @Override
                        public void getCouponInfo(Context context, JSONObject jsonObject) {
                            Log.e("TAG", "getCouponInfo: ");
                        }
                    })
                    .builder());
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void initGpush() {
        try {
            FirebaseMessaging.getInstance().getToken().addOnCompleteListener(new OnCompleteListener<String>() {
                @Override
                public void onComplete(@NonNull Task<String> task) {
                    if (task.isSuccessful()) {
                        String token = task.getResult();
                        LogUtil.e("MainAPP", "zfw initGpush Google_token:" + token);
                        SharedPreferencesUtils.setParam(MainAPP.this, BaseConstants.SaveParameter.DEVICETOKEN, token);
                    }
                }
            });

        }catch (Exception e){
           e.printStackTrace();
        }

    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }

    /**
     * 初始化网络请求
     */
    private void initNetWork() {
        try {
            HttpLoggingInterceptor logInterceptor = new HttpLoggingInterceptor(new HttpLogger());
            logInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

            //获取相应的meta-data标签内容
            String url = Constants.Urls.BASE_FAT_URL;

            ApplicationInfo applicationInfo = getPackageManager().getApplicationInfo(getPackageName(), PackageManager.GET_META_DATA);
            String appType = applicationInfo.metaData.getString("appType");
            if (!TextUtils.isEmpty(appType)) {
                switch (appType) {
                    case "dev":
                        url = Constants.Urls.BASE_DEV_URL;
                        break;
                    case "test":
                        url = Constants.Urls.BASE_TEST_URL;
                        break;
                    case "uat":
                        url = Constants.Urls.BASE_UAT_URL;
                        break;
                    default:
                        url = Constants.Urls.BASE_FAT_URL;
                }
            }
            /**
             * 解决切换网址后引导页不跳转问题：公共key使用的上个网址的，切换网址后、公共key参数解析有误，回调不到success和fail方法，导致无法走后面的逻辑
             */
            //获取上次请求的网址
            String lastUrl = (String) SharedPreferencesUtils.getParam(this, "baseUrl", "");
            //存储本次网址
            SharedPreferencesUtils.setParam(this, "baseUrl", url);
            //两次网址不一样的话就删除公共key
            if (!url.equals(lastUrl)) {
                SharedPreferencesUtils.setParam(this, BaseConstants.SaveParameter.PUBLICKEY, "");
            }
            RetrofitConfig retrofitConfig = new RetrofitConfig.Builder()
                    .setUrl(url)
                    .setReadTime(60)
                    .setWriteTime(60)
                    .setConnectTime(60)
                    .setRetry(true)
                    .setGson(true)
                    .setCookieJar(new PersistentCookieJar(new SetCookieCache(), new SharedPrefsCookiePersistor(this)))
                    .setInterceptor(new Interceptor[]{
                            //请求打印数据的拦截器
                            logInterceptor,
                            //数据加解密的拦截器
                            new PBDataEncryptInterceptor(this, "01")
                    })
                    .builder();

            RetrofitServiceManager.getInstance().init(retrofitConfig);

        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
    }

    /**
     * 初始化ARouter
     */
    private void initARouter() {
        try {
            // 这两行必须写在init之前，否则这些配置在init过程中将无效
            //ARouter.openLog();     // 打印日志
            //ARouter.openDebug();   // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！线上版本需要关闭,否则有安全风险)
            ARouter.init(this);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void initImageLoader() {
        try {
            ImageLoaderManager.getInstance().initImageLoader(new GlideImageLoader());
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        if (level == TRIM_MEMORY_UI_HIDDEN) {
            Glide.get(this).clearMemory();
        }
        Glide.get(this).trimMemory(level);
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        Glide.get(this).clearMemory();
    }
}
