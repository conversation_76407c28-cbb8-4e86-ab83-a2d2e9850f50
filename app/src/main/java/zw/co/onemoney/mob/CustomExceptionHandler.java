package zw.co.onemoney.mob;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;

import om.rrtx.mobile.homemodule.activity.HomeActivity;

public class CustomExceptionHandler implements Thread.UncaughtExceptionHandler {
    private Thread.UncaughtExceptionHandler defaultHandler;
    private Context context;

    public CustomExceptionHandler(Context context) {
        this.context = context;
        this.defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
    }

    @Override
    public void uncaughtException(Thread thread, Throwable throwable) {
        // 向用户展示崩溃信息
        //showCrashDialog(throwable);
        Intent intent = new Intent(context, HomeActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
        // 交给系统的默认处理器
        defaultHandler.uncaughtException(thread, throwable);
    }

    private void showCrashDialog(Throwable throwable) {
        new AlertDialog.Builder(context.getApplicationContext())
                .setTitle("应用崩溃")
                .setMessage("发生错误: " + throwable.getMessage() + "\n请重启应用。")
                .setPositiveButton("确定", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                })
                .setCancelable(false)
                .show();
    }
}
