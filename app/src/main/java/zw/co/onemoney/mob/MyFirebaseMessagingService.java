package zw.co.onemoney.mob;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;

import com.alibaba.android.arouter.launcher.ARouter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;


import java.util.Map;

import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.homemodule.activity.HomeActivity;
import om.rrtx.mobile.loginmodule.activity.LoginActivity;
import om.rrtx.mobile.loginmodule.activity.SplashActivity;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.securitymodule.activity.LoginFingerLockActivity;
import om.rrtx.mobile.securitymodule.activity.LoginPasswordLockActivity;
import om.rrtx.mobile.transfermodule.activity.HistoryActivity;
import om.rrtx.mobile.transfermodule.activity.HistoryAgentMobileActivity;
import om.rrtx.mobile.transfermodule.activity.HistoryDetailsActivity;
import om.rrtx.mobile.transfermodule.activity.HistoryPaymentActivity;
import om.rrtx.mobile.transfermodule.activity.WithdrawalTypeActivity;

/**
 * <AUTHOR> zfw
 * @date : 2023/7/25 14:31
 * @week : 星期二
 * @desc :
 */
public class MyFirebaseMessagingService extends FirebaseMessagingService {
    private static final String TAG = "GoogleFcmService>>>";
    String jsonStr;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.e(TAG, "zfw onCreate>>> 消息服务已开启:");
    }

    //获取到谷歌到token
    @Override
    public void onNewToken(@NonNull String token) {
        super.onNewToken(token);
        Log.e("Google token", "zfw Refreshed token: " + token);
        sendRegistrationToServer(token);

    }

    //  回传给服务器操作
    private void sendRegistrationToServer(String token) {
        SharedPreferencesUtils.setParam(getApplicationContext(), BaseConstants.SaveParameter.DEVICETOKEN, token);
    }

    @Override
    public void onMessageReceived(@NonNull RemoteMessage message) {
        Log.e("GoogleFcmService>>>", "zfw onMessageReceived>>> message:" + message);
        Log.d(TAG, "From: " + message.getFrom());
        jsonStr = JSONObject.toJSONString(message.getData(), SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty);
        // Check if message contains a data payload.
        if (message.getData().size() > 0) {
            Log.d(TAG, "Message data payload: " + message.getData());
            //{language=en, orderId=MP2407291527000041, target=, orderType=32, title={"en":"Transaction Alert","zh":"交易提醒"}}
            try {
                Map<String, String> jsonObject = message.getData();
                if (message.getNotification() != null) {
                    String title = getNotifacationTitle(message.getNotification().getTitle());
                    String content = getNotifacationContent(message.getNotification().getBody());
                    if (!TextUtils.isEmpty(content)) {
                        //发送通知
                        sendNotification(jsonObject, getApplicationContext(), title, content);
                    }
                } else {
                    String title = getNotifacationTitle(jsonObject.get("title"));
                    String content = getNotifacationContent(jsonObject.get("content"));
                    if (!TextUtils.isEmpty(content)) {
                        //发送通知
                        sendNotification(jsonObject, getApplicationContext(), title, content);
                    }
                }

                boolean isBroadcast = (boolean) SharedPreferencesUtils.getParam(getApplicationContext(), BaseConstants.SaveParameter.ISBROADCAST, false);
                if (!isBroadcast) {
                    return;
                }
                //目标页面
                String textToSpeechFlag = jsonObject.get("textToSpeechFlag");

                if (TextUtils.equals(textToSpeechFlag, "0")) {
                    //如果是0的话,不播报.如果是1的情况下播报
                    return;
                }

                //获取推送消息
                String content = jsonObject.get("speechContent");
                //LogUtil.e(TAG, "onNotifyMessageArrived: " + content);
                if (TextUtils.isEmpty(content)) {
                    return;
                }
            } catch (Exception e) {
                //LogUtil.e(TAG, "onNotifyMessageArrived: 解析json异常");
            }
        }

    }

    private String getNotifacationTitle(String title) {
        String titleLast;
        JSONObject titleJson = JSON.parseObject(title);
        String currentLocale = LocaleManager.getInstance().getLanguage(BaseApp.getAPPContext());
        if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_CHINA)) {
            //中文
            titleLast = titleJson.containsKey(LocaleManager.LANGUAGE_CHINA) ? titleJson.getString(LocaleManager.LANGUAGE_CHINA) : titleJson.toString();
            Log.d(TAG, "Message data : " + "，titleLast:" + titleLast);
        } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_ENGLISH)) {
            //英语
            titleLast = titleJson.containsKey(LocaleManager.LANGUAGE_ENGLISH) ? titleJson.getString(LocaleManager.LANGUAGE_ENGLISH) : titleJson.toString();
            Log.d(TAG, "Message data : " + "，titleLast:" + titleLast);
        } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_SHONA)) {
            //Shona
            titleLast = titleJson.containsKey(LocaleManager.LANGUAGE_SHONA) ? titleJson.getString(LocaleManager.LANGUAGE_SHONA) : titleJson.toString();
            Log.d(TAG, "Message data : " + "，titleLast:" + titleLast);
        } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_NDEBELE)) {
            //Ndebele
            titleLast = titleJson.containsKey(LocaleManager.LANGUAGE_NDEBELE) ? titleJson.getString(LocaleManager.LANGUAGE_NDEBELE) : titleJson.toString();
            Log.d(TAG, "Message data : " + "，titleLast:" + titleLast);
        } else {
            //英语
            titleLast = titleJson.containsKey(LocaleManager.LANGUAGE_ENGLISH) ? titleJson.getString(LocaleManager.LANGUAGE_ENGLISH) : titleJson.toString();
            Log.d(TAG, "Message data : " + "，titleLast:" + titleLast);
        }
        return titleLast;
    }

    private String getNotifacationContent(String content) {
        String contentLast;
        JSONObject contentJson = JSON.parseObject(content);
        String currentLocale = LocaleManager.getInstance().getLanguage(BaseApp.getAPPContext());
        if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_CHINA)) {
            //中文
            contentLast = contentJson.containsKey(LocaleManager.LANGUAGE_CHINA) ? contentJson.getString(LocaleManager.LANGUAGE_CHINA) : contentJson.toString();
            Log.d(TAG, "Message data contentLast: " + contentLast);
        } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_ENGLISH)) {
            //英语
            contentLast = contentJson.containsKey(LocaleManager.LANGUAGE_ENGLISH) ? contentJson.getString(LocaleManager.LANGUAGE_ENGLISH) : contentJson.toString();
            Log.d(TAG, "Message data contentLast: " + contentLast);
        } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_SHONA)) {
            //Shona
            contentLast = contentJson.containsKey(LocaleManager.LANGUAGE_SHONA) ? contentJson.getString(LocaleManager.LANGUAGE_SHONA) : contentJson.toString();
            Log.d(TAG, "Message data contentLast: " + contentLast);
        } else if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_NDEBELE)) {
            //Ndebele
            contentLast = contentJson.containsKey(LocaleManager.LANGUAGE_NDEBELE) ? contentJson.getString(LocaleManager.LANGUAGE_NDEBELE) : contentJson.toString();
            Log.d(TAG, "Message data contentLast: " + contentLast);
        } else {
            //英语
            contentLast = contentJson.containsKey(LocaleManager.LANGUAGE_ENGLISH) ? contentJson.getString(LocaleManager.LANGUAGE_ENGLISH) : contentJson.toString();
            Log.d(TAG, "Message data contentLast: " + contentLast);
        }
        return contentLast;
    }

    /**
     * Create and show a simple notification containing the received FCM message.
     *
     * @param jsonObject FCM message body received.
     */
    private void sendNotification(Map<String, String> jsonObject, Context context, String title, String content) {
        Intent intent = null;
        if (ActivityController.getInstance().getStackActivitySize() > 0) {
            String userName = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.USERNAME, "");
            String authorization = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.AUTHORIZATION, "");
            //打开的时候保存相应的信息,这里只会用在推送中见异常的时候
            SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, jsonStr);

            if (TextUtils.isEmpty(userName)) {
                intent = new Intent(context, LoginActivity.class);
                intent.putExtra(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.JPUSHJUMP);
                intent.putExtra(BaseConstants.Transmit.JPUSHJSON, jsonStr);
                intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
            } else {
                //用户名不为空
                if (!TextUtils.isEmpty(authorization)) {
                    //目标页面
                    String target = jsonObject.get("target");
                    Log.d(TAG, "Message data test: " + "，target1:" + target);
                    if (!TextUtils.isEmpty(target) && target.contains("/")) {
                        String[] targetSplit = target.split("/");
                        if (targetSplit.length < 2) {
                            //跳转到首页
                            intent = new Intent(context, HomeActivity.class);
                            intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                            SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, "");
                        } else {
                            handTarget(targetSplit, jsonObject, context);
                        }
                    } else {
                        Log.d(TAG, "Message data test: " + "，target2:" + target);
                        //跳转到首页
                        intent = new Intent(context, HomeActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                        SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, "");
                    }
                } else {
                    boolean isFinger = (boolean) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.ISFINGER, false);
                    if (isFinger) {
                        intent = new Intent(context, LoginFingerLockActivity.class);
                    } else {
                        intent = new Intent(context, LoginPasswordLockActivity.class);
                    }
                    intent.putExtra(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.JPUSHJUMP);
                    intent.putExtra(BaseConstants.Transmit.JPUSHJSON, jsonStr);
                    intent.putExtra(BaseConstants.Transmit.ISPSDBACK, false);
                    intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                }
            }
        } else {
            //应用没有启动
            intent = new Intent(context, SplashActivity.class);
            intent.putExtra(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.JPUSHJUMP);
            intent.putExtra(BaseConstants.Transmit.JPUSHJSON, jsonStr);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        //自定义通知栏
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0 /* Request code */, intent,
                PendingIntent.FLAG_IMMUTABLE);
        String channelId = getString(R.string.app_name);
        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        NotificationCompat.Builder notificationBuilder =
                new NotificationCompat.Builder(this, channelId)
                        .setSmallIcon(R.mipmap.ic_launcher)
                        .setContentTitle(title)
                        .setContentText(content)
                        .setStyle(new NotificationCompat.BigTextStyle().bigText(content))
                        .setAutoCancel(true)
                        .setSound(defaultSoundUri)
                        .setContentIntent(pendingIntent);
        NotificationManager notificationManager =
                (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        // Since android Oreo notification channel is needed.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(channelId,
                    title,
                    NotificationManager.IMPORTANCE_DEFAULT);
            notificationManager.createNotificationChannel(channel);
        }
        notificationManager.notify(0 /* ID of notification */, notificationBuilder.build());
    }

    private void handTarget(String[] targetSplit, Map<String, String> jsonObject, Context context) {
        Intent intent = null;
        if (TextUtils.equals(targetSplit[1], BaseConstants.MessageType.HOME)) {
            //跳转到首页
            intent = new Intent(context, HomeActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
            SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, "");
        } else if (TextUtils.equals(targetSplit[1], BaseConstants.MessageType.TRANSACTION)) {
            //转账类型
            if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONLIST)) {
                //历史账单列表
                intent = new Intent(context, HistoryActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, "");
            } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONDETAIL)) {
                String orderId = jsonObject.get(BaseConstants.Transmit.ORDERID);
                String transType = jsonObject.get(BaseConstants.Transmit.ORDERTYPE);

                if (TextUtils.equals(transType, BaseConstants.HistoryType.PAYTYPE)) {
                    intent = new Intent(context, HistoryPaymentActivity.class);
                    intent.putExtra(BaseConstants.Transmit.ORDERID, orderId);
                    intent.putExtra(BaseConstants.Transmit.ORDERTYPE, transType);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                } else if (TextUtils.equals(transType, BaseConstants.HistoryType.WITHDRAWALTYPE)) {
                    intent = new Intent(context, WithdrawalTypeActivity.class);
                    intent.putExtra(BaseConstants.Transmit.ORDERID, orderId);
                    intent.putExtra(BaseConstants.Transmit.ORDERTYPE, transType);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTTOPUP) || TextUtils.equals(transType, BaseConstants.HistoryType.AGENTWITH)) {
                    ARouter.getInstance().build(ARouterPath.TransferPath.AgentTopUpActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.ORDERID, orderId)
                            .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                            .navigation();
                    intent = new Intent(context, WithdrawalTypeActivity.class);
                    intent.putExtra(BaseConstants.Transmit.ORDERID, orderId);
                    intent.putExtra(BaseConstants.Transmit.ORDERTYPE, transType);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTTRAN)) {
                    ARouter.getInstance().build(ARouterPath.TransferPath.HistoryAgentTranActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.ORDERID, orderId)
                            .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                            .navigation();
                    intent = new Intent(context, WithdrawalTypeActivity.class);
                    intent.putExtra(BaseConstants.Transmit.ORDERID, orderId);
                    intent.putExtra(BaseConstants.Transmit.ORDERTYPE, transType);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTMOBILE)) {
                    intent = new Intent(context, HistoryAgentMobileActivity.class);
                    intent.putExtra(BaseConstants.Transmit.ORDERID, orderId);
                    intent.putExtra(BaseConstants.Transmit.ORDERTYPE, transType);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                } else {
                    intent = new Intent(context, HistoryDetailsActivity.class);
                    intent.putExtra(BaseConstants.Transmit.ORDERID, orderId);
                    intent.putExtra(BaseConstants.Transmit.ORDERTYPE, transType);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                }
            } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONSPILTRECLIST)) {
                //AA我发起的列表
                intent = new Intent(context, HomeActivity.class);
                intent.putExtra(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEAALAUNCH);
                intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, "");
            } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONSPILTPAYLIST)) {
                //AA我支付的列表
                intent = new Intent(context, HomeActivity.class);
                intent.putExtra(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEAARECEIVED);
                intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, "");
            }
        }
    }
}
