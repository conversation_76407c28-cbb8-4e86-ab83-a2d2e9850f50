include ':app',
        ':BaseModule:RrtxCommon', //基础 -> 工具类
        ':BaseModule:FunctionApi', //基础 -> 功能键通信API
        ':Functions:LoginModule', // 功能 -> 登录组件
        ':Functions:HomeModule', // 功能 -> 首页组件
        ':Functions:SecurityModule', // 功能 -> 安全组件
        ':Functions:TransferModule',// 功能 -> 转账
        ':Functions:TopUpModule', //功能 -> 充值
        ':Functions:CashierModel', //功能 -> 收银台模块
        ':Functions:PromotionModel', //功能 -> 推广
        //':Functions:JPushModel', //功能 -> 推送
        ':Functions:GPushModule', //功能 -> G推送
        ':BaseModule:FunctionCommon', //功能 -> 基础功能模块
        ':BaseModule:GoogleMapModule', //功能 -> 地图功能
        ':Functions:XStoreLib', //功能 -> 外部缴费功能
        //':Functions:FaceLib', //功能 -> 人脸
        ":Functions:PaymentModule" //功能 -> 收付款
setBinding(new Binding([gradle: this]))
evaluate(new File(
        settingsDir,
        'xwalletpro_flutter/.android/include_flutter.groovy'
))
rootProject.name = 'Xwallet'
include ':xwalletpro_flutter'
