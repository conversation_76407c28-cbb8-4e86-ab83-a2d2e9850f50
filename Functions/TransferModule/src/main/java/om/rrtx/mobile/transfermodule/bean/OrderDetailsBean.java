package om.rrtx.mobile.transfermodule.bean;

import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class OrderDetailsBean {


    /**
     * orderId : 8098996091732627461
     * tradeType : 10
     * orderStatus : 1
     * tradeTime : 2020-02-21
     * orderIncomeFlag : +
     * orderCurrType : USD
     * tradeAmt : 1000.00
     * recordTitle :
     * remark :
     * tradeMouth :
     * name : <PERSON>jie
     * payMethod :
     */

    private String originalTradeType;
    private String originalOrderId;
    private String channelTransNo;
    private String orderId;
    private String tradeType;
    //除提现以外的交易使用 0-处理中 1-成功 2-失败 3-关闭 4-全额退款  5-部分退款
    //提现订单由于有初始化状态 直接使用原始订单状态 10:初始化 20 :处理中 30 :成功 40:失败
    private String orderStatus;
    private String tradeTime;
    private String orderIncomeFlag = "";
    private String orderCurrType;
    private String tradeAmt;
    private String recordTitle;
    private String remark = "";
    private String tradeMouth;
    // 转账详情用，自适应转账与被转账
    private String name;
    //billPaymentType 02-钱包 03-银行
    private String billPaymentType;
    //支付方式 充值时返回 00-CreditCard/DebetCard
    private String payMethod;
    private String paymentMethod="";
    //提现初使时间
    private String createTime;
    //提现发送渠道处理中时间
    private String sendChannelTime;
    //提现交易完成时间
    private String completeTime;
    //提现银行名称
    private String bankName;
    //提现银行卡
    private String bankCardNo;
    private String cardNo;
    private String merName;
    private String meterName=""; //电表用户名
    //退款原始订单
    private String originalOrderNo;
    //业务类型 05-注册 00-充值
    private String businessType;
    //返现参与人类型 0：发起人   1：推荐人
    private String participantType;
    //返现：发起人 参与人是被推荐人时有此值
    private String initiator;
    //商户订单号
    private String outOrderNo;
    //终端编号
    private String terminalNo;
    //支付时使用的返现金额
    private String cashbackDeductAmount;
    //服务类型名称
    private String serviceType;
    //退款时必须，退款手续费
    private String refundFeeAmt;
    //退款时必须，退款给商户的手续费
    private String refundMerFeeAmt;
    //退款时必须，退款给用户的手续费
    private String refundPayFeeAmt;
    //退款时退回的返现金额
    private String cashbackRefundAmount;
    //商品备注 支付及缴费时使用
    private String commodityRemark;

    /**
     * 1、充值手机号 代客手机充值返回
     * 2、转账时收款方 mobile
     */
    private String recMobile;
    /**
     * 收款时转账方号码
     */
    private String payMobile;

    //代客充值服务商名称
    private String serviceProviderName;
    //代客充值 类型 02-钱包 04-现金
    private String paymentType;
    /**
     * 代客业务 代理商名称
     * branch name
     */
    private String agentName;
    private String agentShortCode;
    // 操作员名称，掩码
    private String agentOperName;


    //支付时必须 商户手续费 扣费方式为内扣时取此值
    private String merFeeAmt;
    //提现|转账转账手续费
    private String feeAmt;
    //转账时必须 收款用户手续费 转账且扣费方式为内扣时展示此字段
    private String recFeeAmt;
    //支付/转账时必须 付款用户手续费  支付且扣费方式为外扣 转账且扣费方式为内扣时展示此字段
    private String payFeeAmt;

    private String taxAmt;
    private String recTaxAmt;
    private String payTaxAmt;
    /**
     * 0 Agent-APP 1 Agent-PC  2 Agent-USSD  3 Customer-APP  4 Customer-USSD
     */
    private String tradingSource;
    // 转账对象名
    private String recCstMame;
    private String billerCode;

    /**
     * 00：话费充值
     * 01：哈拉雷市政
     * 02：电费
     * 03：学费
     * 04：党费
     * 05：社会福利
     * 06：其他
     */
    private String billTypeDesc;
    private String billType;
    private String billerName;  //账单名称
    /**
     * 账单账户（充值手机号、党员号、房产信息、市政账号、电表号、学生名称等
     */
    private String billAccount;
    private String userName;
    private String city;    //房产城市
    private String address; //房产城地址
    private String semester;    //年级
    private String classes; //班级
    private String school;  //学校
    private String enquiryOrderNo;
    private String enquiryAccount;
    private String currency;
    private String resultRemark;
    private String rcvCstName;
    private String payCstName;
    private String payCstType;
    private String recMobileNo;


    // 话费流程新增
    private String bundleName;
    private String bundlePlan;
    private String rechargeTradingType;



    private String paymentProduct;
    private String actualAmt;   // 实付金额
    private String discountAmt;     // 优惠金额
    private String couponAmt;
    private String membershipPointAmt;
    private String membershipBonusAmt;
    private String cashbackAmt;
    private String settlementAmt;
    private String refundAccount;
    private  String transactionMode;
    private String refundTaxAmt;
    private String refundMerTaxAmt;
    private String refundPayTaxAmt;
    private String walletRefundAmount;
    private String relatePayOrderNo;
    private String counterpartyMobileNo;
    private String counterpartyName;
    private String meterNo;// 明文电表号

    public String getRefundAccount() {
        return refundAccount;
    }

    public void setRefundAccount(String refundAccount) {
        this.refundAccount = refundAccount;
    }

    public String getMeterName() {
        return meterName;
    }

    public String getTransactionMode() {
        return transactionMode;
    }

    public void setPayMobile(String payMobile) {
        this.payMobile = payMobile;
    }

    public void setAgentOperName(String agentOperName) {
        this.agentOperName = agentOperName;
    }

    public void setTaxAmt(String taxAmt) {
        this.taxAmt = taxAmt;
    }

    public void setRecTaxAmt(String recTaxAmt) {
        this.recTaxAmt = recTaxAmt;
    }

    public void setPayTaxAmt(String payTaxAmt) {
        this.payTaxAmt = payTaxAmt;
    }

    public void setTradingSource(String tradingSource) {
        this.tradingSource = tradingSource;
    }

    public void setRecCstMame(String recCstMame) {
        this.recCstMame = recCstMame;
    }

    public void setBillerCode(String billerCode) {
        this.billerCode = billerCode;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    public void setBillerName(String billerName) {
        this.billerName = billerName;
    }

    public void setBillAccount(String billAccount) {
        this.billAccount = billAccount;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public void setClasses(String classes) {
        this.classes = classes;
    }

    public void setSchool(String school) {
        this.school = school;
    }

    public void setEnquiryOrderNo(String enquiryOrderNo) {
        this.enquiryOrderNo = enquiryOrderNo;
    }

    public void setEnquiryAccount(String enquiryAccount) {
        this.enquiryAccount = enquiryAccount;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public void setResultRemark(String resultRemark) {
        this.resultRemark = resultRemark;
    }

    public String getRcvCstName() {
        return rcvCstName;
    }

    public void setRcvCstName(String rcvCstName) {
        this.rcvCstName = rcvCstName;
    }

    public String getPayCstName() {
        return payCstName;
    }

    public void setPayCstName(String payCstName) {
        this.payCstName = payCstName;
    }

    public void setPayCstType(String payCstType) {
        this.payCstType = payCstType;
    }

    public void setRecMobileNo(String recMobileNo) {
        this.recMobileNo = recMobileNo;
    }

    public void setBundleName(String bundleName) {
        this.bundleName = bundleName;
    }

    public void setBundlePlan(String bundlePlan) {
        this.bundlePlan = bundlePlan;
    }

    public void setRechargeTradingType(String rechargeTradingType) {
        this.rechargeTradingType = rechargeTradingType;
    }

    public String getPaymentProduct() {
        return paymentProduct;
    }

    public void setPaymentProduct(String paymentProduct) {
        this.paymentProduct = paymentProduct;
    }

    public String getActualAmt() {
        return actualAmt;
    }

    public void setActualAmt(String actualAmt) {
        this.actualAmt = actualAmt;
    }

    public String getDiscountAmt() {
        return discountAmt;
    }

    public void setDiscountAmt(String discountAmt) {
        this.discountAmt = discountAmt;
    }

    public String getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(String couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String getMembershipPointAmt() {
        return membershipPointAmt;
    }

    public void setMembershipPointAmt(String membershipPointAmt) {
        this.membershipPointAmt = membershipPointAmt;
    }

    public String getMembershipBonusAmt() {
        return membershipBonusAmt;
    }

    public void setMembershipBonusAmt(String membershipBonusAmt) {
        this.membershipBonusAmt = membershipBonusAmt;
    }

    public String getCashbackAmt() {
        return cashbackAmt;
    }

    public void setCashbackAmt(String cashbackAmt) {
        this.cashbackAmt = cashbackAmt;
    }

    public String getSettlementAmt() {
        return settlementAmt;
    }

    public void setSettlementAmt(String settlementAmt) {
        this.settlementAmt = settlementAmt;
    }

    public String getRecMobileNo() {
        return recMobileNo;
    }

    public String getAgentOperName() {
        return agentOperName;
    }

    public String getPayCstType() {
        return payCstType;
    }


    public String getBundleName() {
        return bundleName;
    }

    public String getBundlePlan() {
        return bundlePlan;
    }

    public String getRechargeTradingType() {
        return rechargeTradingType;
    }

    public String getResultRemark() {
        return resultRemark;
    }

    public String getEnquiryOrderNo() {
        return enquiryOrderNo;
    }

    public String getEnquiryAccount() {
        return enquiryAccount;
    }


    public String getCurrency() {
        if (!StringUtils.isValidString(currency)) return getOrderCurrType();
        return currency;
    }


    public String getBillerCode() {
        return billerCode;
    }

    public String getBillTypeDesc() {
        return billTypeDesc;
    }

    public void setBillTypeDesc(String billTypeDesc) {
        this.billTypeDesc = billTypeDesc;
    }

    public String getBillType() {
        return billType;
    }

    public String getBillerName() {
        return billerName;
    }

    public String getBillAccount() {
        return billAccount;
    }

    public String getUserName() {
        return userName;
    }

    public String getCity() {
        return city;
    }

    public String getAddress() {
        return address;
    }

    public String getSemester() {
        return semester;
    }

    public String getClasses() {
        return classes;
    }

    public String getSchool() {
        return school;
    }

    public String getTaxAmt() {
        return taxAmt;
    }

    public String getRecTaxAmt() {
        return recTaxAmt;
    }

    public String getPayTaxAmt() {
        return payTaxAmt;
    }

    public String getTradingSource() {
        return tradingSource;
    }

    public String getRecCstMame() {
        return recCstMame;
    }

    public String getChannelTransNo() {
        return channelTransNo;
    }

    public void setChannelTransNo(String channelTransNo) {
        this.channelTransNo = channelTransNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(String tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getOrderIncomeFlag() {
        return orderIncomeFlag;
    }

    public void setOrderIncomeFlag(String orderIncomeFlag) {
        this.orderIncomeFlag = orderIncomeFlag;
    }

    public String getOrderCurrType() {
        return orderCurrType;
    }

    public void setOrderCurrType(String orderCurrType) {
        this.orderCurrType = orderCurrType;
    }

    public String getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(String tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public String getRecordTitle() {
        return recordTitle;
    }

    public void setRecordTitle(String recordTitle) {
        this.recordTitle = recordTitle;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTradeMouth() {
        return tradeMouth;
    }

    public void setTradeMouth(String tradeMouth) {
        this.tradeMouth = tradeMouth;
    }

    public String getName() {
        return name;
    }

    public String getPayMobile() {
        return payMobile;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBillPaymentType() {
        return billPaymentType;
    }

    public void setBillPaymentType(String billPaymentType) {
        this.billPaymentType = billPaymentType;
    }

    public String getOriginalOrderId() {
        return originalOrderId;
    }

    public void setOriginalOrderId(String originalOrderId) {
        this.originalOrderId = originalOrderId;
    }

    public String getPayMethod() {
        if (!StringUtils.isValidString(payMethod)) return paymentMethod;
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getSendChannelTime() {
        return sendChannelTime;
    }

    public void setSendChannelTime(String sendChannelTime) {
        this.sendChannelTime = sendChannelTime;
    }

    public String getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(String completeTime) {
        this.completeTime = completeTime;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(String feeAmt) {
        this.feeAmt = feeAmt;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getBankCardNo() {
        if (!StringUtils.isValidString(bankCardNo))return cardNo;
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getOriginalTradeType() {
        return originalTradeType;
    }

    public void setOriginalTradeType(String originalTradeType) {
        this.originalTradeType = originalTradeType;
    }

    public String getMerName() {
        return merName;
    }

    public void setMerName(String merName) {
        this.merName = merName;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getMerFeeAmt() {
        return merFeeAmt;
    }

    public void setMerFeeAmt(String merFeeAmt) {
        this.merFeeAmt = merFeeAmt;
    }

    public String getPayFeeAmt() {
        return payFeeAmt;
    }

    public void setPayFeeAmt(String payFeeAmt) {
        this.payFeeAmt = payFeeAmt;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getParticipantType() {
        return participantType;
    }

    public void setParticipantType(String participantType) {
        this.participantType = participantType;
    }

    public String getInitiator() {
        return initiator;
    }

    public void setInitiator(String initiator) {
        this.initiator = initiator;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getTerminalNo() {
        return terminalNo;
    }

    public void setTerminalNo(String terminalNo) {
        this.terminalNo = terminalNo;
    }

    public String getRecFeeAmt() {
        return recFeeAmt;
    }

    public void setRecFeeAmt(String recFeeAmt) {
        this.recFeeAmt = recFeeAmt;
    }

    public String getCashbackDeductAmount() {
        return cashbackDeductAmount;
    }

    public void setCashbackDeductAmount(String cashbackDeductAmount) {
        this.cashbackDeductAmount = cashbackDeductAmount;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getRefundFeeAmt() {
        return refundFeeAmt;
    }

    public void setRefundFeeAmt(String refundFeeAmt) {
        this.refundFeeAmt = refundFeeAmt;
    }

    public String getRefundMerFeeAmt() {
        return refundMerFeeAmt;
    }

    public void setRefundMerFeeAmt(String refundMerFeeAmt) {
        this.refundMerFeeAmt = refundMerFeeAmt;
    }

    public String getRefundPayFeeAmt() {
        return refundPayFeeAmt;
    }

    public void setRefundPayFeeAmt(String refundPayFeeAmt) {
        this.refundPayFeeAmt = refundPayFeeAmt;
    }

    public String getCashbackRefundAmount() {
        return cashbackRefundAmount;
    }

    public void setCashbackRefundAmount(String cashbackRefundAmount) {
        this.cashbackRefundAmount = cashbackRefundAmount;
    }

    public String getCommodityRemark() {
        return commodityRemark;
    }

    public void setCommodityRemark(String commodityRemark) {
        this.commodityRemark = commodityRemark;
    }

    public String getRecMobile() {
        return recMobile;
    }

    public void setRecMobile(String recMobile) {
        this.recMobile = recMobile;
    }

    public String getServiceProviderName() {
        return serviceProviderName;
    }

    public void setServiceProviderName(String serviceProviderName) {
        this.serviceProviderName = serviceProviderName;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getAgentShortCode() {
        return agentShortCode;
    }

    public void setAgentShortCode(String agentShortCode) {
        this.agentShortCode = agentShortCode;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getRefundTaxAmt() {
        return refundTaxAmt;
    }

    public void setRefundTaxAmt(String refundTaxAmt) {
        this.refundTaxAmt = refundTaxAmt;
    }

    public String getRefundMerTaxAmt() {
        return refundMerTaxAmt;
    }

    public void setRefundMerTaxAmt(String refundMerTaxAmt) {
        this.refundMerTaxAmt = refundMerTaxAmt;
    }

    public String getRefundPayTaxAmt() {
        return refundPayTaxAmt;
    }

    public void setRefundPayTaxAmt(String refundPayTaxAmt) {
        this.refundPayTaxAmt = refundPayTaxAmt;
    }

    public String getWalletRefundAmount() {
        return walletRefundAmount;
    }

    public void setWalletRefundAmount(String walletRefundAmount) {
        this.walletRefundAmount = walletRefundAmount;
    }

    public String getRelatePayOrderNo() {
        return relatePayOrderNo;
    }

    public void setRelatePayOrderNo(String relatePayOrderNo) {
        this.relatePayOrderNo = relatePayOrderNo;
    }

    public String getCounterpartyMobileNo() {
        return counterpartyMobileNo;
    }

    public void setCounterpartyMobileNo(String counterpartyMobileNo) {
        this.counterpartyMobileNo = counterpartyMobileNo;
    }

    public String getCounterpartyName() {
        return counterpartyName;
    }

    public void setCounterpartyName(String counterpartyName) {
        this.counterpartyName = counterpartyName;
    }

    public String getMeterNo() {
        return meterNo;
    }

    public void setMeterNo(String meterNo) {
        this.meterNo = meterNo;
    }

    @Override
    public String toString() {
        return "OrderDetailsBean{" +
                "orderId='" + orderId + '\'' +
                ", tradeType='" + tradeType + '\'' +
                ", orderStatus='" + orderStatus + '\'' +
                ", tradeTime='" + tradeTime + '\'' +
                ", orderIncomeFlag='" + orderIncomeFlag + '\'' +
                ", orderCurrType='" + orderCurrType + '\'' +
                ", tradeAmt='" + tradeAmt + '\'' +
                ", recordTitle='" + recordTitle + '\'' +
                ", remark='" + remark + '\'' +
                ", tradeMouth='" + tradeMouth + '\'' +
                ", name='" + name + '\'' +
                ", payMethod='" + payMethod + '\'' +
                ", paymentMethod='" + paymentMethod + '\'' +
                ", createTime='" + createTime + '\'' +
                ", sendChannelTime='" + sendChannelTime + '\'' +
                ", completeTime='" + completeTime + '\'' +
                ", bankName='" + bankName + '\'' +
                ", bankCardNo='" + bankCardNo + '\'' +
                ", cardNo='" + cardNo + '\'' +
                ", merName='" + merName + '\'' +
                ", meterName='" + meterName + '\'' +
                ", originalOrderNo='" + originalOrderNo + '\'' +
                ", businessType='" + businessType + '\'' +
                ", participantType='" + participantType + '\'' +
                ", initiator='" + initiator + '\'' +
                ", outOrderNo='" + outOrderNo + '\'' +
                ", terminalNo='" + terminalNo + '\'' +
                ", cashbackDeductAmount='" + cashbackDeductAmount + '\'' +
                ", serviceType='" + serviceType + '\'' +
                ", refundFeeAmt='" + refundFeeAmt + '\'' +
                ", refundMerFeeAmt='" + refundMerFeeAmt + '\'' +
                ", refundPayFeeAmt='" + refundPayFeeAmt + '\'' +
                ", cashbackRefundAmount='" + cashbackRefundAmount + '\'' +
                ", commodityRemark='" + commodityRemark + '\'' +
                ", recMobile='" + recMobile + '\'' +
                ", payMobile='" + payMobile + '\'' +
                ", serviceProviderName='" + serviceProviderName + '\'' +
                ", paymentType='" + paymentType + '\'' +
                ", agentName='" + agentName + '\'' +
                ", agentOperName='" + agentOperName + '\'' +
                ", merFeeAmt='" + merFeeAmt + '\'' +
                ", feeAmt='" + feeAmt + '\'' +
                ", recFeeAmt='" + recFeeAmt + '\'' +
                ", payFeeAmt='" + payFeeAmt + '\'' +
                ", taxAmt='" + taxAmt + '\'' +
                ", recTaxAmt='" + recTaxAmt + '\'' +
                ", payTaxAmt='" + payTaxAmt + '\'' +
                ", tradingSource='" + tradingSource + '\'' +
                ", recCstMame='" + recCstMame + '\'' +
                ", billerCode='" + billerCode + '\'' +
                ", billType='" + billType + '\'' +
                ", billerName='" + billerName + '\'' +
                ", billAccount='" + billAccount + '\'' +
                ", userName='" + userName + '\'' +
                ", city='" + city + '\'' +
                ", address='" + address + '\'' +
                ", semester='" + semester + '\'' +
                ", classes='" + classes + '\'' +
                ", school='" + school + '\'' +
                ", enquiryOrderNo='" + enquiryOrderNo + '\'' +
                ", enquiryAccount='" + enquiryAccount + '\'' +
                ", currency='" + currency + '\'' +
                ", resultRemark='" + resultRemark + '\'' +
                ", rcvCstName='" + rcvCstName + '\'' +
                ", payCstName='" + payCstName + '\'' +
                ", payCstType='" + payCstType + '\'' +
                ", recMobileNo='" + recMobileNo + '\'' +
                ", bundleName='" + bundleName + '\'' +
                ", bundlePlan='" + bundlePlan + '\'' +
                ", rechargeTradingType='" + rechargeTradingType + '\'' +
                ", paymentProduct='" + paymentProduct + '\'' +
                ", actualAmt='" + actualAmt + '\'' +
                ", discountAmt='" + discountAmt + '\'' +
                ", couponAmt='" + couponAmt + '\'' +
                ", membershipPointAmt='" + membershipPointAmt + '\'' +
                ", membershipBonusAmt='" + membershipBonusAmt + '\'' +
                ", cashbackAmt='" + cashbackAmt + '\'' +
                ", settlementAmt='" + settlementAmt + '\'' +
                ", transactionMode='" + transactionMode + '\'' +
                ", refundTaxAmt='" + refundTaxAmt + '\'' +
                ", refundMerTaxAmt='" + refundMerTaxAmt + '\'' +
                ", refundPayTaxAmt='" + refundPayTaxAmt + '\'' +
                ", walletRefundAmount='" + walletRefundAmount + '\'' +
                ", relatePayOrderNo='" + relatePayOrderNo + '\'' +
                ", counterpartyMobileNo='" + counterpartyMobileNo + '\'' +
                ", counterpartyName='" + counterpartyName + '\'' +
                ", meterNo='" + meterNo + '\'' +
                '}';
    }
}
