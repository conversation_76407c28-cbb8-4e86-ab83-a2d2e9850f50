package om.rrtx.mobile.transfermodule;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import om.rrtx.mobile.functioncommon.bean.CurrencyAccountListBean;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.transfermodule.bean.AccCheckBean;
import om.rrtx.mobile.transfermodule.bean.BankAccountListBean;
import om.rrtx.mobile.transfermodule.bean.ByMobileBean;
import om.rrtx.mobile.transfermodule.bean.CalculateBean;
import om.rrtx.mobile.transfermodule.bean.ChangeFlagBean;
import om.rrtx.mobile.transfermodule.bean.ContactBean;
import om.rrtx.mobile.transfermodule.bean.HistoryAccountListBean;
import om.rrtx.mobile.transfermodule.bean.HistoryListBean;
import om.rrtx.mobile.transfermodule.bean.OrderDetailsBean;
import om.rrtx.mobile.transfermodule.bean.PubBean;
import om.rrtx.mobile.transfermodule.bean.RegionBean;
import om.rrtx.mobile.transfermodule.bean.TransferBean;
import om.rrtx.mobile.transfermodule.bean.WithdrawalCheckBean;
import om.rrtx.mobile.transfermodule.bean.WithdrawalOrderBean;
import retrofit2.Response;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

/**
 * 登录的Api接口层
 */
public interface TransferService {

    /**
     * 获取公钥的接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.GETPUB)
    Observable<Response<BaseBean<PubBean>>> requestPub(@FieldMap Map<String, String> formData);


    /**
     * 查询最近5个转账联系人
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.LATEST5TRANSFERUSER)
    Observable<Response<BaseBean<TransferBean>>> requestLatest5TransferUser(@FieldMap Map<String, String> formData);

    /**
     * 根据手机号查询用户信息
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.GETBYMOBILE)
    Observable<Response<BaseBean<ByMobileBean>>> requestByMobile(@FieldMap Map<String, String> formData);

    /**
     * 个人用户转账订单校验接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.TRANSFERTOPERACCCHECK)
    Observable<Response<BaseBean<AccCheckBean>>> requestTransfer(@FieldMap Map<String, String> formData);

    /**
     * 个人用户转账
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.TRANSFERTOPERACC)
    Observable<Response<BaseBean<Object>>> requestUserTransfer(@FieldMap Map<String, String> formData);

    /**
     * 添加联系人
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.ADDCONTACT)
    Observable<Response<BaseBean<Object>>> requestAddContact(@FieldMap Map<String, String> formData);

    /**
     * 删除联系人
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.DELETECONTACT)
    Observable<Response<BaseBean<Object>>> requestDeleteContact(@FieldMap Map<String, String> formData);

    /**
     * 获取联系人列表
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.CONTACTLIST)
    Observable<Response<BaseBean<ContactBean>>> requestContactList(@FieldMap Map<String, String> formData);

    /**
     * 查询联系人是否需要更新
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.ISCONTACTCHANGE)
    Observable<Response<BaseBean<ChangeFlagBean>>> requestIsContactChange(@FieldMap Map<String, String> formData);

    /**
     * 历史列表页面
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.HISTORYLIST)
    Observable<Response<BaseBean<HistoryListBean>>> requestHistoryList(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(TransferConstants.URL.JUNIOR_HISTORYLIST)
    Observable<Response<BaseBean<HistoryListBean>>> requestJuniorHistoryList(@FieldMap Map<String, String> formData);

    /**
     * 获取订单详情
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.ORDERLIST)
    Observable<Response<BaseBean<OrderDetailsBean>>> requestOrderDetailsList(@FieldMap Map<String, String> formData);

    /**
     * 请求时区接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.RAGION)
    Observable<Response<BaseBean<List<RegionBean>>>> requestRegion(@FieldMap Map<String, String> formData);

    /**
     * 上次提现银行卡信息
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.LASTWITHDRAWALCARD)
    Observable<Response<BaseBean<BankAccountListBean.CardRecordsBean>>> requestLastBank(@FieldMap Map<String, String> formData);

    /**
     * 查询用户金额
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.BALANCE)
    Observable<Response<BaseBean<CurrencyAccountListBean>>> requestBalance(@FieldMap Map<String, String> formData);

    /**
     * 提现订单校验
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.WITHDRAWALCHECK)
    Observable<Response<BaseBean<WithdrawalCheckBean>>> requestWithdrawalCheck(@FieldMap Map<String, String> formData);

    /**
     * Zipit提现申请验证接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.ZIPITCHECK)
    Observable<Response<BaseBean<WithdrawalCheckBean>>> requestZIPITCheck(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(TransferConstants.URL.ZIPITLOOKUP)
    Observable<Response<BaseBean<WithdrawalCheckBean>>> requestZIPITLookup(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(TransferConstants.URL.CASHOUTCHECK)
    Observable<Response<BaseBean<WithdrawalCheckBean>>> requestCashOutCheck(@FieldMap Map<String, String> formData);


    /**
     * 创建提现订单
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.WITHDRAWALCREATEORDER)
    Observable<Response<BaseBean<WithdrawalOrderBean>>> requestWithdrawalOrder(@FieldMap Map<String, String> formData);

    /**
     * 获取银行账户
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.BANKLISTURL)
    Observable<Response<BaseBean<BankAccountListBean>>> requestBankList(@FieldMap Map<String, String> formData);

    /**
     * 删除银行卡接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.BANKDELETE)
    Observable<Response<BaseBean<Object>>> requestDeleteBank(@FieldMap Map<String, String> formData);

    /**
     * 获取银行的Url
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.BINDCARDREQUEST)
    Observable<Response<BaseBean<Object>>> requestBankUrl(@FieldMap Map<String, String> formData);

    /**
     * binding银行卡接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.BINDBANK)
    Observable<Response<BaseBean<Object>>> requestBindBank(@FieldMap Map<String, String> formData);

    /**
     * 账户变动列表
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(TransferConstants.URL.USERBALANCERECORDS)
    Observable<Response<BaseBean<HistoryAccountListBean>>> getBalanceList(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(TransferConstants.URL.CHECK_QUERY_TRANSFER_ORDER)
    Observable<Response<BaseBean<HistoryAccountListBean>>> checkQueryTransferOrder(@FieldMap Map<String, String> formData);

}
