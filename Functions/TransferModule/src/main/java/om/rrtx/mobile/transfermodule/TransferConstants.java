package om.rrtx.mobile.transfermodule;

import om.rrtx.mobile.functioncommon.CommonConstants;

public interface TransferConstants {
    /**
     * 传递的参数
     */
    public interface Transmit {
        /**
         * 金额
         */
        String PRICE = "price";
        /**
         * 转账的相应数据
         */
        String JSON = CommonConstants.Transmit.JSON;
        /**
         * 标题
         */
        String TITLERES = "titleRes";
        /**
         * 返回按钮
         */
        String BACKRES = "backRes";
        /**
         * 订单Id
         */
        String ORDERID = "orderId";
        /**
         * 订单类型
         */
        String ORDERTYPE = "orderType";
        String RECORD_TITLE = "recordTitle";
        /**
         * 订单详情页面中展示的姓名
         */
        String SHOWNAME = "showName";
        /**
         * 订单详情页面中展示的电话
         */
        String SHOWMOBILE = "showMobile";
        /**
         * 订单详情页面中展示的头像
         */
        String SHOWAVATAR = "showAvatar";
        /**
         * 卡号
         */
        String CARDNO = "cardNo";
    }

    interface Parameter {
        /**
         * 加密字段标识
         */
        String KEYID = "keyId";
        /**
         * 电话号码
         */
        String MOBILE = "mobile";
        /**
         * 转账人电话
         */
        String RCVMOBILE = CommonConstants.Parameter.RCVMOBILE;
        /**
         * 转账金额
         */
        String TRANSFERAMT = "transferAmt";
        /**
         * 转账留言
         */
        String REMARK = "remark";
        /**
         * 转账的密码
         */
        String PAYMENTPASSWORD = "paymentPassword";
        /**
         * 转账的密码
         */
        String TRANSFERTOKEN = "transferToken";
        /**
         * 日期
         */
        String TRADEMOUTH = "tradeMouth";
        /**
         * 类型
         */
        String TRADETYPE = "tradeType";
        /**
         * 页数
         */
        String PAGENUM = "pageNum";
        String PAGE = "page";
        /**
         * 最大索引数
         */
        String PAGESIZE = "pageSize";
        String LIMIT = "limit";
        /**
         * 订单id
         */
        String ORDERID = "orderId";
        /**
         * 客户类型
         */
        String CSTTYPE = "cstType";
        /**
         * 金额
         */
        String TRANSAMT = "transAmt";
        String TRX_AMT = "trxAmt";
        /**
         * 交易类型
         */
        String TRANSTYPE = "transType";
        /**
         * 付款方式
         */
        String PAYMENTTYPE = "paymentType";
        /**
         * 银行名称
         */
        String BANKNO = "bankNo";
        /**
         * 银行编码
         */
        String BANK_CODE = "bankCode";

        /**
         * 卡名称
         */
        String BANKNAME = "bankName";
        /**
         * 卡号
         */
        String CARDNO = "cardNo";
        /**
         * 金额
         */
        String AMT = "amt";
        /**
         * 费率
         */
        String FEEAMT = "feeAmt";
        /**
         * 密码方式
         */
        String PAYMENTPASSWORDTYPE = "paymentPasswordType";
        /**
         * 银行卡类型
         */
        String CARDTYPE = "cardType";
        /**
         * 联系人token
         */
        String HASHTOKEN = "hashToken";
        /**
         * 交易双方手机号
         */
        String TARGETMOBILE = "targetMobile";
        /**
         * 用户id
         */
        String USERID = "userId";
        String USER_TYPE = "userType";
        /**
         * 余额动账日期 202002
         */
        String BALANCECHANGEMOUTH = "balanceChangeMouth";
        /**
         * 账户类型 224103-基本户 224106-用户返现户
         */
        String PARENTACCNO = "parentAccNo";
        /**
         * 货币
         */
        String CURRENCY = "currency";
        String REMITTANCECODE = "remittanceCode";
        String CERTIFICATE = "certificate";
    }

    interface URL {
        /**
         * 获取公钥接口
         */
        String GETPUB = "/encrypt/public/key";
        /**
         * 查询最近5个转账联系人
         */
        String LATEST5TRANSFERUSER = "/perTransfer/latest5TransferUser";
        /**
         * 根据手机号查询用户信息
         */
        String GETBYMOBILE = "/user/getByMobile";
        /**
         * 个人用户转账订单校验接口
         */
        String TRANSFERTOPERACCCHECK = "/perTransfer/transferToPerAccCheck";
        /**
         * 个人用户转账订单校验接口
         */
        String TRANSFERTOPERACC = "/perTransfer/transferToPerAcc";
        /**
         * 添加联系人
         */
        String ADDCONTACT = "/contact/add";
        /**
         * 删除联系人
         */
        String DELETECONTACT = "/contact/delete";
        /**
         * 联系人列表
         */
        String CONTACTLIST = "/contact/list";
        /**
         * 联系人是否需要更新
         */
        String ISCONTACTCHANGE = "/contact/isContactChange";
        /**
         * 订单列表
         */
        String HISTORYLIST = "/orderRecord/list";
        /**
         * 订单列表
         */
        String JUNIOR_HISTORYLIST = "/juniorSubjectInf/list";
        /**
         * 订单详情
         */
        String ORDERLIST = "/orderRecord/detail";
        /**
         * 请求时区接口
         */
        String RAGION = "/nation/list";
        /**
         * 上次提现银行卡信息
         */
        String LASTWITHDRAWALCARD = "/withdrawal/lastWithdrawalCard";
        /**
         * 查询用户金额
         */
        String BALANCE = "/subjectInf/balance";

        /**
         * 创建提现订单
         */
        String WITHDRAWALCREATEORDER = "/withdrawal/createOrder";
        /**
         * 银行卡类型
         */
        String BANKLISTURL = "/cstBank/cstBankListQuery";
        /**
         * 删除银行卡接口
         */
        String BANKDELETE = "/cstCardRecord/del";
        /**
         * 绑定银行卡
         */
        String BINDCARDREQUEST = "/cstCardRecord/bindCardRequest";
        /**
         * 绑定一行卡接口
         */
        String BINDBANK = "/cstCardRecord/add";
        /**
         * 提现校验
         */
        String WITHDRAWALCHECK = "/withdrawal/check";
        /**
         * Zipit提现申请验证接口
         */
        String ZIPITCHECK = "/user/zipit/check";
        String ZIPITLOOKUP = "/user/zipit/lookup";
        String CASHOUTCHECK = "/cashOut/check";
        /**
         * 账户变动列表
         */
        String USERBALANCERECORDS = "/subjectInf/userBalanceRecords";
        String CHECK_QUERY_TRANSFER_ORDER = "/orderRecord/queryOrderRecordPayCheck";
    }

    /**
     * 历史选择的类型
     */
    public interface HistoryType {
        //所有
        String ALL = "All";
        String ALLTYPE = "";
        //充值
        String TOPUP = "Top Up";
        String TOPUPTYPE = "00";
        //转账
        String FUNDTRANSFER = "Fund Transfer";
        String FUNDTRANSFERTYPE = "10";
        //收款
        String WITHDRAWAL = "Withdrawal";
        String WITHDRAWALTYPE = "20";
        //支付
        String PAY = "Payment";
        String PAYTYPE = "30";
        //缴费
        String BILLPAYMENT = "31";
        String MOBILE_CHARGE = "32";
        //账单
        String SPLITBILL = "Split Bill";
        String SPLITBILLTYPE = "40";
        //退款
        String REFUND = "Refund";
        String REFUNDTYPE = "50";
        //返现
        String CASHBACK = "cashBack";
        String CASHBACKTYPE = "55";
        //代客充值
        String AGENTTOPUP = "01";
        //代客话费充值
        String AGENTMOBILE = "02";
        //代客转账
        String AGENTTRAN = "11";
        //代客转账退款
        String AGENTTRANREFUND = "12";
        //代客提现
        String AGENTWITH = "22";

        //充值信用卡支付
        String TOPCREDITCARDTYPE = "00";
    }

    /**
     * 订单状态
     */
    interface OrderStatus {
        String PENDING = "0";   // 处理中
        String SUCCESS = "1";
        String FAIL = "2";
        String CLOSED = "3";
        String FULLREFUND = "4"; // 全额退款
        String PARTIALREFUND = "5"; // 部分退款
    }

    interface WithdrawalOrderStatus {
        //10 初始化 20 处理中 30成功 40 失败
        String INITIATE = "10";
        String PROCESSING = "20";
        String SUCCESS = "30";
        String FAIL = "40";
    }

    interface CstType {
        //客户类型：0-商户，1 用户  充值及提现传1   支付传0 其他类型待补充
        String WithdrawalType = "0";
        String PayType = "1";
    }

    interface PaymentType {
        //支付类型：Online Banking-00，Offline Top Up-01，Ewallet-02，Bank-03
        String OnlineBanking = "00";
        String OfflineTopUp = "01";
        String Ewallet = "02";
        String Bank = "03";
    }

    interface PaymentProduct {
        //交易类型为支付时需要）APP-00，H5-01，Bar Code Payment-02，Qr Code Payment-03
        String App = "00";
        String H5 = "01";
        String BarCodePayment = "02";
        String QrCodePayment = "03";
    }
}
