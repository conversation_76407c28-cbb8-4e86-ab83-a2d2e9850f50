package om.rrtx.mobile.transfermodule.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.transfermodule.R;
import om.rrtx.mobile.transfermodule.TransferConstants;
import om.rrtx.mobile.transfermodule.bean.HistoryTypeBean;

/**
 * <AUTHOR>
 * 历史类别的选择框
 */
public class HistoryTypeRVAdapter extends RecyclerView.Adapter<BaseHolder> {

    private Context mContext;
    private List<HistoryTypeBean> mBeanList;
    private int selectIndex = 0;
    private RVAdapterItemClickListener<HistoryTypeBean> mClickListener;

    public HistoryTypeRVAdapter(Context context) {
        mContext = context;
        mBeanList = new ArrayList<>();
        //账户类型 0-常规账户 1-亲子账户
        String accountType= (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE,"0");
        if ("1".equals(accountType))
        {
            setJuniorData();
        }else{
            setData();
        }
    }

    private void setJuniorData() {
        mBeanList.add(new HistoryTypeBean(TransferConstants.HistoryType.ALLTYPE,
                mContext.getString(R.string.history_btn_all)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Payment,
                mContext.getString(R.string.history_label_payment)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Refund,
                mContext.getString(R.string.history_label_refund)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Transfer,
                mContext.getString(R.string.send_money)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Mobile_Fee,
                mContext.getString(R.string.buy_Airtime_Bundle)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Bill_PAY,
                mContext.getString(R.string.history_label_bill_pay)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Query_BALANCE,
                mContext.getString(R.string.balance_Enquiry)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Query_Transfer,
                mContext.getString(R.string.statement_Enquiry)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Agent_Transfer,
                mContext.getString(R.string.history_label_agent_tran)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Agent_TopUp,
                mContext.getString(R.string.history_label_agent_topup)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.CASH_OUT,
                mContext.getString(R.string.history_label_agent_with)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Agent_MOBILLE,
                mContext.getString(R.string.history_label_agent_mobile)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Agent_Zesa,
                mContext.getString(R.string.agent_Bill_Payemnt)));
        /*mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.TopUp,
                mContext.getString(R.string.bank_to_OneMoney)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Withdraw,
                mContext.getString(R.string.oneMoney_to_Bank)));*/

        /*mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Bulk_Payment,
                mContext.getString(R.string.bulk_Payment)));*/

        /*mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.AAPayment,
                mContext.getString(R.string.history_label_split_bill)));*/
    }

    /**
     * 因为这里是静态的,所以这里就不对外暴露了
     */
    private void setData() {
        mBeanList.add(new HistoryTypeBean(TransferConstants.HistoryType.ALLTYPE,
                mContext.getString(R.string.history_btn_all)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.TopUp,
                mContext.getString(R.string.bank_to_OneMoney)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Withdraw,
                mContext.getString(R.string.oneMoney_to_Bank)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Transfer,
                mContext.getString(R.string.send_money)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Transfer_Reversal,
                mContext.getString(R.string.send_money_reversal)));
        /*mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Bulk_Payment,
                mContext.getString(R.string.bulk_Payment)));*/
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Payment,
                mContext.getString(R.string.history_label_payment)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Refund,
                mContext.getString(R.string.history_label_refund)));
        /*mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.AAPayment,
                mContext.getString(R.string.history_label_split_bill)));*/
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Mobile_Fee,
                mContext.getString(R.string.buy_Airtime_Bundle)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Bill_PAY,
                mContext.getString(R.string.history_label_bill_pay)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Query_BALANCE,
                mContext.getString(R.string.balance_Enquiry)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Query_Transfer,
                mContext.getString(R.string.statement_Enquiry)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Agent_MOBILLE,
                mContext.getString(R.string.history_label_agent_mobile)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Agent_TopUp,
                mContext.getString(R.string.history_label_agent_topup)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.CASH_OUT,
                mContext.getString(R.string.history_label_agent_with)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Agent_Transfer,
                mContext.getString(R.string.history_label_agent_tran)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Agent_Zesa,
                mContext.getString(R.string.agent_Bill_Payemnt)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.ZIPIT,
                mContext.getString(R.string.zipit_title)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Thirty_Party_Paying,
                mContext.getString(R.string.thirty_title)));
        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.ZIPIT_Reversal,
                mContext.getString(R.string.zipit_reversal_title)));

//        mBeanList.add(new HistoryTypeBean(CommonConstants.TransType.Cashback,
//                mContext.getString(R.string.history_btn_cashback)));
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.transfer_item_history_type, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        HistoryTypeBean historyTypeBean = mBeanList.get(position);

        //设置显示内容
        TextView type = holder.getView(R.id.typeTv);
        type.setText(historyTypeBean.getName());

        //设置选中和未选中状态
        if (selectIndex == position) {
            //选中状态
            type.setBackgroundResource(R.drawable.common_btn_stroke);
            type.setTextColor(mContext.getResources().getColor(R.color.common_ye_F3881E));
        } else {
            //未选中状态
            type.setBackgroundResource(R.drawable.transfer_drawable_his_type_un_select);
            type.setTextColor(mContext.getResources().getColor(R.color.common_text_1d2129));
        }

        holder.itemView.setOnClickListener((view) -> {
            selectIndex = position;
            if (mClickListener != null) {
                mClickListener.itemClickListener(historyTypeBean, position);
            }
            notifyDataSetChanged();
        });
    }

    @Override
    public int getItemCount() {
        return mBeanList.size();
    }

    public void setClickListener(RVAdapterItemClickListener<HistoryTypeBean> clickListener) {
        mClickListener = clickListener;
    }

    public void resetIndex() {
        selectIndex = 0;
        notifyDataSetChanged();
    }
}
