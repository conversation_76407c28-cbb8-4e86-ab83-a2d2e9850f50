package om.rrtx.mobile.transfermodule.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import om.rrtx.mobile.functioncommon.bean.OptionsBankListBean
import om.rrtx.mobile.functioncommon.model.CommonModel
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver
import om.rrtx.mobile.rrtxcommon1.net.BaseObserverNoError
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil
import om.rrtx.mobile.transfermodule.bean.BankAccountListBean
import om.rrtx.mobile.transfermodule.bean.WithdrawalCheckBean
import om.rrtx.mobile.transfermodule.model.TransferModel

class ZipitVM : ViewModel() {

    val optionsBankListBean = MutableLiveData<OptionsBankListBean>()
    val bankAccountListBean = MutableLiveData<BankAccountListBean>()
    val orderCheckResult = MutableLiveData<WithdrawalCheckBean>()
    val lookUpResult = MutableLiveData<String>()

    fun getOptionsBankList(queryType: String) {
        val currentActivity = ActivityController.getInstance().currentActivity()
        CommonModel().getOptionsBankList(queryType,
            object : BaseObserver<OptionsBankListBean>(currentActivity) {
                override fun requestSuccess(sResData: OptionsBankListBean) {
                    optionsBankListBean.value = sResData
                }

                override fun requestFail(sResMsg: String?) {
                    ToastUtil.show(currentActivity, sResMsg)
                }

            })
    }

    fun getDirectBankAccount(bankCode: String, currency: String) {
        val currentActivity = ActivityController.getInstance().currentActivity()
        TransferModel().getDirectBankAccount(bankCode, currency,
            object : BaseObserver<BankAccountListBean>(currentActivity) {
                override fun requestSuccess(sResData: BankAccountListBean) {
                    bankAccountListBean.value = sResData
                }

                override fun requestFail(sResMsg: String) {

                }

            })
    }

    fun requestZIPITCheck(transAmt: String, currency: String) {
        val currentActivity = ActivityController.getInstance().currentActivity()
        TransferModel().requestZIPITCheck(transAmt, currency, object :
            BaseObserver<WithdrawalCheckBean>(currentActivity) {
            override fun requestSuccess(sResData: WithdrawalCheckBean) {
                orderCheckResult.value = sResData
            }

            override fun requestFail(sResMsg: String?) {
                ToastUtil.show(currentActivity, sResMsg)
            }

        })
    }

    fun requestZIPITLookup(bankNo: String, currency: String, cardNo: String) {
        val currentActivity = ActivityController.getInstance().currentActivity()
        TransferModel().requestZIPITLookup(bankNo, currency, cardNo,object :
            BaseObserver<WithdrawalCheckBean>(currentActivity) {
            override fun requestSuccess(sResData: WithdrawalCheckBean) {
                lookUpResult.value = sResData.userName
            }

            override fun requestErrorBody(body: BaseBean<WithdrawalCheckBean>) {
                if (body.status == "RRP-********") {
                    lookUpResult.value = "RRP-********"
                }else{
                    ToastUtil.show(currentActivity, body.message)
                }
            }

            override fun requestFail(sResMsg: String?) {
            }

        })
    }
}