package om.rrtx.mobile.transfermodule.activity

import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.transfer_activity_zipit.account_card
import kotlinx.android.synthetic.main.transfer_activity_zipit.bankAccount_ed
import kotlinx.android.synthetic.main.transfer_activity_zipit.bankName_tv
import kotlinx.android.synthetic.main.transfer_activity_zipit.bankView
import kotlinx.android.synthetic.main.transfer_activity_zipit.bg_view1
import kotlinx.android.synthetic.main.transfer_activity_zipit.currency_tv
import kotlinx.android.synthetic.main.transfer_activity_zipit.include_title
import kotlinx.android.synthetic.main.transfer_activity_zipit.moneyTv
import kotlinx.android.synthetic.main.transfer_activity_zipit.nextTv
import kotlinx.android.synthetic.main.transfer_activity_zipit.remark_ed
import kotlinx.android.synthetic.main.transfer_activity_zipit.rv
import kotlinx.android.synthetic.main.transfer_activity_zipit.textClear_iv
import kotlinx.android.synthetic.main.transfer_base_title.backIv
import kotlinx.android.synthetic.main.transfer_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.bean.BankInfoVo
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean
import om.rrtx.mobile.functioncommon.bean.WithdrawalOrderBean
import om.rrtx.mobile.functioncommon.callback.CashierCallBack
import om.rrtx.mobile.functioncommon.dialog.CommonCurrencyDialog
import om.rrtx.mobile.functioncommon.utils.CashierManager
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder
import om.rrtx.mobile.rrtxcommon1.bean.OrderSuccessBean
import om.rrtx.mobile.rrtxcommon1.dialog.BottomDialogSelectDialog
import om.rrtx.mobile.rrtxcommon1.dialog.DoubleBigDialog
import om.rrtx.mobile.rrtxcommon1.dialog.DoubleDialog
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.BigDecimalUtils
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SoftKeyBoardListener
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil
import om.rrtx.mobile.transfermodule.R
import om.rrtx.mobile.transfermodule.bean.BankAccountListBean
import om.rrtx.mobile.transfermodule.bean.WithdrawalCheckBean
import om.rrtx.mobile.transfermodule.databinding.ItemBankAccountBinding
import om.rrtx.mobile.transfermodule.databinding.TransferActivityZipitBinding
import om.rrtx.mobile.transfermodule.utils.EditInputFilter
import om.rrtx.mobile.transfermodule.viewmodel.ZipitVM
import java.math.BigDecimal


@Route(path = ARouterPath.TransferPath.ZipitTransActivity)
class ZipitTransActivity : BaseActivity<TransferActivityZipitBinding>() {

    private var mCurrency: String = ""
    private lateinit var mVM: ZipitVM
    private lateinit var selectBank: BankInfoVo
    private var listBean: List<BankAccountListBean.CardRecordsBean> = ArrayList()

    private var selectBankAccount: BankAccountListBean.CardRecordsBean =
        BankAccountListBean.CardRecordsBean()
    private lateinit var mCashierManager: CashierManager
    //lateinit var bankAccountSelectDialog: BankAccountSelectDialog
    private var selectCardNo: String = ""

    fun isFixCurrency() = StringUtils.isValidString(mCurrency)
    override fun createContentView() = R.layout.transfer_activity_zipit

    override fun doGetExtra() {
        val currency = intent.getStringExtra(BaseConstants.Transmit.CURRENCY)
        currency?.also { mCurrency = CurrencyUtils.setCurrency(mContext,it) }
    }

    override fun initWorkspaceAction() {
        mVM = ViewModelProvider(this).get(ZipitVM::class.java)

        //键盘的监听
        SoftKeyBoardListener.setListener(
            mContext,
            object : SoftKeyBoardListener.OnSoftKeyBoardChangeListener {
                override fun keyBoardShow(height: Int) {
                    //键盘显示
                    Log.e("done", "keyBoardShow: ")
                }

                override fun keyBoardHide(height: Int) {
                    Log.e("done", "keyBoardHide: ")
                    //键盘隐藏的时候设置相应的金额
                    var mAmountStr = moneyTv.text.toString();
                    if (!TextUtils.isEmpty(mAmountStr)) {
                        mAmountStr = BigDecimalUtils.getStandardAmount(
                            mAmountStr,
                            2,
                            BigDecimal.ROUND_HALF_UP
                        );
                        moneyTv.setText(mAmountStr);
                        moneyTv.setSelection(mAmountStr.length);
                    }
                }
            })
    }

    override fun initView() {
        ImmersionBar.with(this).statusBarView(R.id.statusView).statusBarDarkFont(true, 0.2f).init()

        include_title.apply {
            backIv.setImageResource(R.drawable.common_ic_back_black)
            titleTv.setBackgroundColor(resources.getColor(R.color.color_FFFFFF))
            titleTv.setTextColor(resources.getColor(R.color.color_131313))
            titleTv.setText(R.string.zipit_title)
        }

        if (isFixCurrency()) {
            currency_tv.text = mCurrency
        } else {
            val drawableLeft = ResourceHelper.getDrawable(R.drawable.ic_extend)
            drawableLeft?.setBounds(0, 0, 14.pt2px(), 8.pt2px())
            currency_tv.setCompoundDrawables(null, null, drawableLeft, null);
            currency_tv.compoundDrawablePadding = 10.pt2px()
        }

        //设置EditText的过滤
        val filters = arrayOf<InputFilter>(EditInputFilter(mContext))
        moneyTv.filters = filters

        initDialog()
    }

    private fun initDialog() {
        /*bankAccountSelectDialog = BankAccountSelectDialog(this@ZipitTransActivity,
            bg_view2,
            ArrayList(),
            object : BankAccountSelectDialog.DialogCallBack {
                override fun onClick(bean: BankAccountListBean.CardRecordsBean) {
                    selectBankAccount = bean
                    bankAccount_ed.setText(StringUtils.stringBankModileMask(bean.cardNo))
                    orderIsFull()
                    // 获取当前LayoutParams
                    val layoutParams: ViewGroup.MarginLayoutParams =
                        nextTv.layoutParams as ViewGroup.MarginLayoutParams
                    layoutParams.topMargin = 100.pt2px()
                    // 将修改后的LayoutParams应用到View
                    nextTv.layoutParams = layoutParams
                }
            })*/
    }

    override fun initListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View) {
                when (view) {
                    backIv -> {
                        onBackPressed()
                    }

                    currency_tv -> {
                        val mCommonCurrencyDialog =
                            CommonCurrencyDialog(
                                this@ZipitTransActivity
                            )
                        mCommonCurrencyDialog.setOnCurrencyClickListener(CommonCurrencyDialog.OnCurrencyClickListener { currency -> // 赋值货币符号
                            currency_tv.text = currency
                        })
                        mCommonCurrencyDialog.show()
                    }

                    bg_view1 -> {
                        mVM.getOptionsBankList("3")
                    }

                    nextTv -> {
                        var mAmountStr = moneyTv.text.toString()
                        if (!TextUtils.isEmpty(mAmountStr)) {
                            mAmountStr = BigDecimalUtils.getStandardAmount(
                                mAmountStr,
                                2,
                                BigDecimal.ROUND_HALF_UP
                            )
                            moneyTv.setText(mAmountStr)
                            moneyTv.setSelection(mAmountStr.length)
                            if (TextUtils.equals(mAmountStr, "0") || TextUtils.equals(
                                    mAmountStr,
                                    "0.00"
                                )
                            ) {
                                ToastUtil.show(
                                    mContext,
                                    resources.getString(R.string.payment_tip_amount_must_greater) + mAmountStr
                                )
                                return
                            }

                            var cardNo = selectBankAccount.cardNo
                            if (TextUtils.isEmpty(cardNo)){
                                cardNo = bankAccount_ed.text.toString()
                            }
                            mVM.requestZIPITLookup(
                                selectBank.bankCode,
                                currency_tv.text.toString(),
                                cardNo
                            )
                        }
                    }

                    textClear_iv -> {
                        bankAccount_ed.text.clear()
                        bankAccount_ed.requestFocus()
                        val inputMethodManager =
                            mContext.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
                        inputMethodManager.showSoftInput(
                            currentFocus,
                            InputMethodManager.SHOW_FORCED
                        )

                        if (listBean.isNotEmpty()) {
                            showDialog()
                        } /*else {
                            val inputMethodManager =
                                mContext.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
                            inputMethodManager.showSoftInput(
                                currentFocus,
                                InputMethodManager.SHOW_FORCED
                            )
                        }*/
                    }
                }
            }

        }.apply {
            if (!isFixCurrency()) currency_tv.setOnClickListener(this)
            bg_view1.setOnClickListener(this)
            nextTv.setOnClickListener(this)
            backIv.setOnClickListener(this)
            textClear_iv.setOnClickListener(this)
        }

        bankAccount_ed.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                val regex = Regex("[0-9A-Za-z]{6,30}")
                val bankNo = editable.toString()
                val b = bankNo.matches(regex)
                //  银行卡号校验
                orderIsFull(b)
                if (b) {
                    //selectBankAccount.cardNo = bankNo
                    selectCardNo = bankNo
                }
                textClear_iv.visibility = if (bankNo.isNotEmpty()) View.VISIBLE else View.GONE
                if (bankNo.isEmpty()) {
                    val inputMethodManager =
                        mContext.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
                    inputMethodManager.showSoftInput(
                        currentFocus,
                        InputMethodManager.SHOW_FORCED
                    )

                    if (listBean.isNotEmpty()) {
                        showDialog()
                    }
                }
            }

        })

        moneyTv.onFocusChangeListener = View.OnFocusChangeListener { view, b ->
            if (!b) {
                var mAmountStr = moneyTv.text.toString()
                if (!TextUtils.isEmpty(mAmountStr)) {
                    mAmountStr =
                        BigDecimalUtils.getStandardAmount(mAmountStr, 2, BigDecimal.ROUND_HALF_UP)
                    moneyTv.setText(mAmountStr)
                    moneyTv.setSelection(mAmountStr.length)
                }
            }
        }
        moneyTv.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                orderIsFull()
            }

        })
        initVMListener()
    }

    private var mDoubleDialog: DoubleBigDialog? = null
    private fun showHitDialog(msg: String) {
        if (mDoubleDialog == null) {
            mDoubleDialog = DoubleBigDialog(mContext)
            mDoubleDialog!!.setDoubleCallback(object : DoubleBigDialog.DoubleCallback {
                override fun leftCallback() {
                    mDoubleDialog!!.dismiss()
                }

                override fun rightCallback() {
                    mDoubleDialog!!.dismiss()
                    mVM.requestZIPITCheck(
                        moneyTv.text.toString(),
                        currency_tv.text.toString()
                    )
                }
            })
        }
        mDoubleDialog!!.show()
        mDoubleDialog!!.setLeftStr(getString(R.string.cancel_transaction))
            .setLeftColor(resources.getColor(R.color.color_F85A40))
            .setRightStr(getString(R.string.proceed_transaction))
            .setRightColor(resources.getColor(R.color.common_ye_F3881E))
            .setTitle(getString(R.string.common_alert_prompt))
        if (msg == "RRP-********"){
            mDoubleDialog!!.setContentStr(getString(R.string.lookup_hint))
        }else{
            mDoubleDialog!!.setContentStr(getString(R.string.account_holder) + msg)
        }

    }
    private fun initVMListener() {
        mVM.optionsBankListBean.observe(this) {
            if (it != null && it.bankInfs.isNotEmpty()) {
                val data = it.bankInfs
                val strings = ArrayList<String>()
                for (element in data) {
                    strings.add(element.bankName)
                }
                val mDialog = BottomDialogSelectDialog(mContext, strings, 0)
                mDialog.setClickListener { value, position ->
                    selectBank = data[position]
                    bankName_tv.text = selectBank.bankName
                    bankAccount_ed.text.clear()
                    mVM.getDirectBankAccount(selectBank.bankCode, currency_tv.text.toString())

                }
                mDialog.show()
            }
        }

        mVM.bankAccountListBean.observe(this) {
            if (it != null && it.records.isNotEmpty()) {
                listBean = it.records
                showDialog()
            } else {
                listBean = emptyList()
                //bankAccountSelectDialog.hide()
                bankView.visibility = View.GONE
                account_card.cardElevation = 0f
            }
        }

        mVM.orderCheckResult.observe(this) {
            if (it != null) {
                checkBanToOrderSuccess(it)
            }
        }

        mVM.lookUpResult.observe(this) {
            if (it != null) {
                showHitDialog(it)
            }
        }
    }

    lateinit var mAdapter: RecyclerView.Adapter<RecyclerView.ViewHolder>
    private fun showDialog() {
        //bankAccountSelectDialog.show(listBean)
        bankView.visibility = View.VISIBLE
        account_card.cardElevation = 5f
        setBankView()
        initBankView()
        if (listBean.size > 3) {
            // 获取当前LayoutParams
            val layoutParams: ViewGroup.MarginLayoutParams =
                nextTv.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = 540.pt2px()
            // 将修改后的LayoutParams应用到View
            nextTv.layoutParams = layoutParams
        } else if (listBean.size == 3) {
            // 获取当前LayoutParams
            val layoutParams: ViewGroup.MarginLayoutParams =
                nextTv.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = 460.pt2px()
            // 将修改后的LayoutParams应用到View
            nextTv.layoutParams = layoutParams
        } else if (listBean.size == 2) {
            // 获取当前LayoutParams
            val layoutParams: ViewGroup.MarginLayoutParams =
                nextTv.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = 380.pt2px()
            // 将修改后的LayoutParams应用到View
            nextTv.layoutParams = layoutParams
        } else if (listBean.size == 1) {
            // 获取当前LayoutParams
            val layoutParams: ViewGroup.MarginLayoutParams =
                nextTv.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = 300.pt2px()
            // 将修改后的LayoutParams应用到View
            nextTv.layoutParams = layoutParams
        }
    }

    private fun initBankView() {
        rv.apply {
            layoutManager = LinearLayoutManager(context)
            mAdapter = object : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
                override fun onCreateViewHolder(parent: ViewGroup,
                                                viewType: Int): RecyclerView.ViewHolder {
                    val inflate = LayoutInflater.from(context)
                        .inflate(R.layout.item_bank_account, parent, false)
                    ItemBankAccountBinding.bind(inflate)
                    return BaseHolder(inflate)
                }

                override fun getItemCount() = listBean.size

                override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
                    val binding =
                        DataBindingUtil.getBinding<ItemBankAccountBinding>(holder.itemView)
                            ?: return
                    val bankNo = StringUtils.stringBankModileMask(listBean[position].cardNo)
                    binding.bankNo.text = bankNo
                    binding.root.setOnClickListener(object : CustomClickListener() {
                        override fun onSingleClick(view: View?) {
                            selectBankAccount = listBean[position]
                            bankAccount_ed.setText(StringUtils.stringBankModileMask(listBean[position].cardNo))
                            orderIsFull()
                            // 获取当前LayoutParams
                            val layoutParams: ViewGroup.MarginLayoutParams =
                                nextTv.layoutParams as ViewGroup.MarginLayoutParams
                            layoutParams.topMargin = 100.pt2px()
                            // 将修改后的LayoutParams应用到View
                            nextTv.layoutParams = layoutParams
                            bankView.visibility = View.GONE
                            account_card.cardElevation = 0f
                        }
                    })
                }
            }
            adapter = mAdapter
        }
    }

    private fun setBankView() {
        if (listBean.size>3){
            // 获取当前LayoutParams
            val layoutParams: ViewGroup.LayoutParams = rv.layoutParams
            layoutParams.height = 352.pt2px()
            // 将修改后的LayoutParams应用到View
            rv.layoutParams = layoutParams
        }else if (listBean.size==3){
            // 获取当前LayoutParams
            val layoutParams: ViewGroup.LayoutParams = rv.layoutParams
            layoutParams.height = 264.pt2px()
            // 将修改后的LayoutParams应用到View
            rv.layoutParams = layoutParams
        }else if (listBean.size==2){
            // 获取当前LayoutParams
            val layoutParams: ViewGroup.LayoutParams = rv.layoutParams
            layoutParams.height = 176.pt2px()
            // 将修改后的LayoutParams应用到View
            rv.layoutParams = layoutParams
        }else if (listBean.size==1){
            // 获取当前LayoutParams
            val layoutParams: ViewGroup.LayoutParams = rv.layoutParams
            layoutParams.height = 88.pt2px()
            // 将修改后的LayoutParams应用到View
            rv.layoutParams = layoutParams
        }
    }

    /**
     * 提现订单校验成功
     */
    private fun checkBanToOrderSuccess(sResData: WithdrawalCheckBean) {
        var cardNo = selectBankAccount.cardNo
        if (TextUtils.isEmpty(cardNo)){
            cardNo = bankAccount_ed.text.toString()
        }
        var orderAmt: String = moneyTv.text.toString()
        orderAmt = BigDecimalUtils.getStandardAmount(orderAmt, 2, BigDecimal.ROUND_HALF_UP)
        val cashierOrderInfoBean = CashierOrderInfoBean.Builder()
            .setOrderInfo(getString(R.string.zipit_title))
            .setPayType(CommonConstants.CashierPaymentType.ZIPIT)
            .setOrderAmt(orderAmt)
            .setOrderSource(BaseConstants.OrderSourceType.INSIDE)
            .setPaymentToken(sResData.withdrawalToken)
            .setCurrency(CurrencyUtils.setCurrency(mContext, currency_tv.text.toString()))
            .setBankName(selectBank.bankName)
            .setCardNo(cardNo)
            .setBankNo(selectBank.bankCode)
            .setTransType(CommonConstants.TransType.ZIPIT)
            .setRemark(remark_ed.text.toString())
            .setPaymentProduct(CommonConstants.PaymentProduct.APP)
            .builder()
        mCashierManager =
            CashierManager(this, Gson().toJson(cashierOrderInfoBean), object : CashierCallBack {
                override fun cancelOrderPay() {
                    mCashierManager.dismiss()
                }

                override fun forgotCallBack() {

                }

                override fun paymentSuccess(dataJson: String) {

                    val fromJson = Gson().fromJson(dataJson, WithdrawalOrderBean::class.java)
                    var orderSuccessBean =
                        OrderSuccessBean(
                            orderType = CommonConstants.CashierPaymentType.ZIPIT,
                            currency = fromJson.currency,
                            actAmount = fromJson.amt,
                            isFixCur = isFixCurrency(),
                            failedMsg = fromJson.failMsg,
                            isTradeSuccess = fromJson.trxStatus == "30"
                        )

                    ARouter.getInstance().build(ARouterPath.TransferPath.ComTransferSuccessActivity)
                        .withString(
                            CommonConstants.Transmit.ORDER_SUCCESS_BRAN,
                            Gson().toJson(orderSuccessBean)
                        )
                        .navigation()
                }

                override fun paymentFailed(message: String) {
                    /*val orderSuccessBean =
                        OrderSuccessBean(
                            CommonConstants.CashierPaymentType.ZIPIT,
                            isTradeSuccess = false,
                            failedMsg = message,
                            isFixCur = isFixCurrency()
                        )
                    ARouter.getInstance().build(ARouterPath.TransferPath.ComTransferSuccessActivity)
                        .withString(
                            CommonConstants.Transmit.ORDER_SUCCESS_BRAN,
                            Gson().toJson(orderSuccessBean)
                        )
                        .navigation()*/
                }
            })
        mCashierManager.showCashierDialog()
    }

    fun orderIsFull(isValidAccount: Boolean = true) {
        Log.e("amount", "moneyTv==" + moneyTv.text.toString())
        var isMoney = moneyTv.text.trim()
            .isNotEmpty() && moneyTv.text.toString() != "0.00" && moneyTv.text.toString() != "0"
        if (isMoney &&
            bankName_tv.text.isNotEmpty() &&
            bankAccount_ed.text.isNotEmpty() &&
            isValidAccount
        ) {
            nextTv.isEnabled = true
            nextTv.setBackgroundResource(R.drawable.common_usable_btn)
        } else {
            nextTv.isEnabled = false
            nextTv.setBackgroundResource(R.drawable.common_unusable_btn)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            //bankAccountSelectDialog.dismiss()
            bankView.visibility = View.GONE
            account_card.cardElevation = 0f
        } catch (e: Exception) {

        }
    }
}
