package om.rrtx.mobile.transfermodule.model;

import android.text.TextUtils;

import java.security.PublicKey;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.CurrencyAccountListBean;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.rrtxcommon1.net.ApiException;
import om.rrtx.mobile.rrtxcommon1.net.BaseLoader;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserverNoError;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitServiceManager;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;
import om.rrtx.mobile.rrtxcommon1.utils.RSAUtils;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.transfermodule.TransferConstants;
import om.rrtx.mobile.transfermodule.TransferService;
import om.rrtx.mobile.transfermodule.bean.AccCheckBean;
import om.rrtx.mobile.transfermodule.bean.BankAccountListBean;
import om.rrtx.mobile.transfermodule.bean.ByMobileBean;
import om.rrtx.mobile.transfermodule.bean.CalculateBean;
import om.rrtx.mobile.transfermodule.bean.ChangeFlagBean;
import om.rrtx.mobile.transfermodule.bean.ContactBean;
import om.rrtx.mobile.transfermodule.bean.HistoryAccountListBean;
import om.rrtx.mobile.transfermodule.bean.HistoryListBean;
import om.rrtx.mobile.transfermodule.bean.OrderDetailsBean;
import om.rrtx.mobile.transfermodule.bean.PubBean;
import om.rrtx.mobile.transfermodule.bean.RegionBean;
import om.rrtx.mobile.transfermodule.bean.TransferBean;
import om.rrtx.mobile.transfermodule.bean.WithdrawalCheckBean;
import om.rrtx.mobile.transfermodule.bean.WithdrawalOrderBean;
import retrofit2.Response;

/**
 * <AUTHOR>
 * 登录模块的整天请求
 */
public class TransferModel extends BaseLoader {

    private TransferService mTransferService;

    private String mWithdrawalToken;

    public TransferModel() {
        mTransferService = RetrofitServiceManager.getInstance().create(TransferService.class);
    }

    public void commonPub(BaseObserver<PubBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mTransferService.requestPub(map)).subscribe(baseObserver);
    }

    /**
     * 查询最近5个转账联系人
     */
    public void requestLatest5TransferUser(String userId, BaseObserver<TransferBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestLatest5TransferUser(map)).subscribe(baseObserver);
    }

    /**
     * 根据手机号查询用户信息
     */
    public void requestByMobile(String userId, String mobile, BaseObserver<ByMobileBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.MOBILE, mobile);
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestByMobile(map)).subscribe(baseObserver);
    }

    /**
     * 个人用户转账订单校验接口
     */
    public void requestTransfer(String userId, String mobile, String amount, String remark, String currency, BaseObserver<AccCheckBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.RCVMOBILE, mobile);
        map.put(TransferConstants.Parameter.TRANSFERAMT, amount);
        map.put(TransferConstants.Parameter.USERID, userId);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        if (!TextUtils.isEmpty(remark)) {
            map.put(TransferConstants.Parameter.REMARK, remark);
        }
        observe(mTransferService.requestTransfer(map)).subscribe(baseObserver);
    }

    /**
     * 个人用户转账
     */
    public void requestUserTransfer(String mobile, String amount, String remark, String paymentPassword, String transferToken, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.RCVMOBILE, mobile);
        map.put(TransferConstants.Parameter.TRANSFERAMT, amount);
        map.put(TransferConstants.Parameter.PAYMENTPASSWORD, paymentPassword);
        map.put(TransferConstants.Parameter.TRANSFERTOKEN, transferToken);
        if (!TextUtils.isEmpty(remark)) {
            map.put(TransferConstants.Parameter.REMARK, remark);
        }
        observe(mTransferService.requestUserTransfer(map)).subscribe(baseObserver);
    }

    /**
     * 添加联系人
     */
    public void requestAddContact(String userId, String mobile, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.MOBILE, mobile);
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestAddContact(map)).subscribe(baseObserver);
    }

    /**
     * 删除联系人
     */
    public void requestDeleteContact(String userId, String mobile, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.MOBILE, mobile);
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestDeleteContact(map)).subscribe(baseObserver);
    }

    /**
     * 查询联系人列表
     */
    public void requestContactList(String userId, BaseObserver<ContactBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestContactList(map)).subscribe(baseObserver);
    }

    /**
     * 查询联系人是否需要更新
     */
    public void requestIsContactChange(String userId, String hashToken, BaseObserver<ChangeFlagBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(TransferConstants.Parameter.HASHTOKEN, hashToken);
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestIsContactChange(map)).subscribe(baseObserver);
    }

    /**
     * 获取订单详情
     */
    public void requestOrderDetailsList(String userId, String orderId, String tradeType, BaseObserver<OrderDetailsBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(TransferConstants.Parameter.ORDERID, orderId);
        map.put(TransferConstants.Parameter.TRADETYPE, tradeType);
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestOrderDetailsList(map)).subscribe(baseObserver);
    }

    /**
     * 历史列表页面
     */
    public void requestHistoryList(String tradeMouth, String tradeType, String currency, String certificate, int pageNum, int pageSize, BaseObserver<HistoryListBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(TransferConstants.Parameter.TRADEMOUTH, tradeMouth);
        map.put(TransferConstants.Parameter.TRADETYPE, tradeType);
        map.put(TransferConstants.Parameter.PAGENUM, String.valueOf(pageNum));
        map.put(TransferConstants.Parameter.PAGESIZE, String.valueOf(pageSize));
        map.put(TransferConstants.Parameter.CURRENCY, currency);
        map.put(TransferConstants.Parameter.CERTIFICATE, certificate);
//        String s = SharedPreferencesUtils.getParam(ActivityController.getInstance().currentActivity(), BaseConstants.Transmit.JUNIOR_ID, "").toString();
//        map.put(CommonConstants.Parameter.USERID, s);
        observe(mTransferService.requestHistoryList(map)).subscribe(baseObserver);
    }

    public void requestJuniorHistoryList(String tradeMouth, String tradeType,String currency, String userId, String certificate,int pageNum, int pageSize, BaseObserver<HistoryListBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(TransferConstants.Parameter.TRADEMOUTH, tradeMouth);
        map.put(TransferConstants.Parameter.TRADETYPE, tradeType);
        map.put(TransferConstants.Parameter.PAGENUM, String.valueOf(pageNum));
        map.put(TransferConstants.Parameter.PAGESIZE, String.valueOf(pageSize));
        map.put(TransferConstants.Parameter.CURRENCY, currency);
        map.put(TransferConstants.Parameter.CERTIFICATE, certificate);
        map.put(CommonConstants.Parameter.JUNIOR_USERID, userId);
        observe(mTransferService.requestJuniorHistoryList(map)).subscribe(baseObserver);
    }

    /**
     * 历史列表页面
     */
    public void requestHistoryTypeList(String userId, String tradeMouth, String tradeType, String targetMobile, int pageNum, int pageSize, BaseObserver<HistoryListBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(TransferConstants.Parameter.TARGETMOBILE, targetMobile);
        map.put(TransferConstants.Parameter.TRADEMOUTH, tradeMouth);
        map.put(TransferConstants.Parameter.TRADETYPE, tradeType);
        map.put(TransferConstants.Parameter.PAGENUM, String.valueOf(pageNum));
        map.put(TransferConstants.Parameter.PAGESIZE, String.valueOf(pageSize));
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestHistoryList(map)).subscribe(baseObserver);
    }

    /**
     * 获取时区接口
     */
    public void requestRegion(BaseObserver<List<RegionBean>> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mTransferService.requestRegion(map)).subscribe(baseObserver);
    }

    /**
     * 获取时区接口
     */
    public void requestLastBank(String userId, BaseObserver<BankAccountListBean.CardRecordsBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestLastBank(map)).subscribe(baseObserver);
    }

    /**
     * 请求账户余额接口
     *
     * @param baseObserver 回调
     */
    public void requestBalance(String userId, BaseNoDialogObserver<CurrencyAccountListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestBalance(map)).subscribe(baseObserver);
    }

    public void newRequestCalculate(String transAmt, String currency, BaseObserver<WithdrawalCheckBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.AMT, transAmt);
        map.put(TransferConstants.Parameter.CURRENCY, currency);
        observe(mTransferService.requestWithdrawalCheck(map)).subscribe(baseObserver);
    }

    public void requestZIPITCheck(String transAmt, String currency, BaseObserver<WithdrawalCheckBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.AMT, transAmt);
        map.put(TransferConstants.Parameter.CURRENCY, currency);
        observe(mTransferService.requestZIPITCheck(map)).subscribe(baseObserver);
    }

    public void requestZIPITLookup(String bankNo, String currency, String cardNo, BaseObserver<WithdrawalCheckBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.BANKNO, bankNo);
        map.put(TransferConstants.Parameter.CURRENCY, currency);
        map.put(TransferConstants.Parameter.CARDNO, cardNo);
        observe(mTransferService.requestZIPITLookup(map)).subscribe(baseObserver);
    }

    public void requestCashOutCheck(String transAmt, String currency, String remittanceCode,BaseObserver<WithdrawalCheckBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.AMT, transAmt);
        map.put(TransferConstants.Parameter.CURRENCY, currency);
        map.put(TransferConstants.Parameter.REMITTANCECODE, remittanceCode);
        observe(mTransferService.requestCashOutCheck(map)).subscribe(baseObserver);
    }

    /**
     * 获取银行账户
     */
    public void requestBankList(String page, String limit, BaseObserver<BankAccountListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
//        map.put(TransferConstants.Parameter.PAGE, page);
//        map.put(TransferConstants.Parameter.LIMIT, limit);
        observe(mTransferService.requestBankList(map)).subscribe(baseObserver);
    }

    public void getDirectBankAccount(String bankCode, String currency, BaseObserver<BankAccountListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        if (!TextUtils.isEmpty(bankCode)) {
            map.put(TransferConstants.Parameter.BANK_CODE, bankCode);
        }
        map.put(TransferConstants.Parameter.CURRENCY, currency);
        observe(mTransferService.requestBankList(map)).subscribe(baseObserver);
    }

    /**
     * 删除卡接口
     *
     * @param cardNo 卡号
     */
    public void requestDeleteBank(String userId, String cardNo, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.CARDNO, cardNo);
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestDeleteBank(map)).subscribe(baseObserver);
    }

    /**
     * 删除卡接口
     */
    public void requestBankUrl(String userId, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestBankUrl(map)).subscribe(baseObserver);
    }

    /**
     * 绑定卡接口
     *
     * @param cardNo 卡号
     */
    public void requestBindBank(String userId, String cardNo, String cardType, String bankNo, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.CARDNO, cardNo);
        map.put(TransferConstants.Parameter.CARDTYPE, cardType);
        map.put(TransferConstants.Parameter.BANKNO, bankNo);
        map.put(TransferConstants.Parameter.USERID, userId);
        observe(mTransferService.requestBindBank(map)).subscribe(baseObserver);
    }

    /**
     * 账户变动列表
     */
    public void getBalanceList(String balanceChangeMouth, String parentAccNo, int pageNum, int pageSize, String currency, String certificate, BaseObserver<HistoryAccountListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.BALANCECHANGEMOUTH, balanceChangeMouth);
        map.put(TransferConstants.Parameter.PARENTACCNO, parentAccNo);
        map.put(TransferConstants.Parameter.PAGENUM, String.valueOf(pageNum));
        map.put(TransferConstants.Parameter.PAGESIZE, String.valueOf(pageSize));
        map.put(TransferConstants.Parameter.CURRENCY, currency);
        map.put(TransferConstants.Parameter.CERTIFICATE, certificate);
        observe(mTransferService.getBalanceList(map)).subscribe(baseObserver);
    }

    public void checkQueryTransferOrder(String currency, String amt, BaseObserver<HistoryAccountListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(TransferConstants.Parameter.USER_TYPE, "00");
        map.put(TransferConstants.Parameter.CURRENCY, currency);
        map.put(TransferConstants.Parameter.TRX_AMT, amt);
        map.put(TransferConstants.Parameter.TRANSTYPE, "66");
        observe(mTransferService.checkQueryTransferOrder(map)).subscribe(baseObserver);
    }
}
