package om.rrtx.mobile.transfermodule.activity.history

import android.text.TextUtils
import android.view.View
import android.widget.TextView
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.CommonConstants.BillPayType.ELE_FEES
import om.rrtx.mobile.functioncommon.CommonConstants.BillPayType.HARARE
import om.rrtx.mobile.functioncommon.CommonConstants.BillPayType.MOBILE_FEES
import om.rrtx.mobile.functioncommon.CommonConstants.BillPayType.OTHER
import om.rrtx.mobile.functioncommon.CommonConstants.BillPayType.PARTY_FEES
import om.rrtx.mobile.functioncommon.CommonConstants.BillPayType.SCHOOL_FEES
import om.rrtx.mobile.functioncommon.CommonConstants.BillPayType.SOCIAL_WELFARE
import om.rrtx.mobile.rrtxcommon1.BaseApp
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils
import om.rrtx.mobile.rrtxcommon1.utils.MoneyUtil
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.TimeUtils
import om.rrtx.mobile.transfermodule.R
import om.rrtx.mobile.transfermodule.TransferConstants
import om.rrtx.mobile.transfermodule.adapter.DiscountBean
import om.rrtx.mobile.transfermodule.adapter.HistoryDetailsItemBean
import om.rrtx.mobile.transfermodule.bean.OrderDetailsBean
import om.rrtx.mobile.transfermodule.utils.TransferUiConstants
import java.math.BigDecimal


class HistoryDetailsDataHelper {
    companion object {

        @JvmField
        val payFeesTitleMap = HashMap<String, String>().apply {
            put(MOBILE_FEES, getString(R.string.buy_airtime))
            put(HARARE,
                getString(R.string.bill_Payment) + " - " + getString(R.string.city_of_Harare))
            put(ELE_FEES, getString(R.string.bill_Payment) + " - " + getString(R.string.zESA))
            put(SCHOOL_FEES,
                getString(R.string.bill_Payment) + " - " + getString(R.string.school_Fees))
            put(PARTY_FEES, getString(R.string.bill_Payment) + " - " + getString(R.string.zANU_PF))
            put(SOCIAL_WELFARE,
                getString(R.string.bill_Payment) + " - " + getString(R.string.social_Amenities))
            put(OTHER, getString(R.string.bill_Payment) + " - " + getString(R.string.others))
        }

        @JvmStatic
        fun setTitle(mOrderType: String,
                     tradeTypeName: String,
            rootView: View,
            bean: OrderDetailsBean) {
            val titleTv = rootView.findViewById<TextView>(R.id.title)
            executeSetTitle(mOrderType, tradeTypeName, titleTv, bean)
            setMoneyAndStatus(mOrderType, rootView, bean)
        }

        private fun executeSetTitle(mOrderType: String,
                                    tradeTypeName: String,
            titleTv: TextView,
            bean: OrderDetailsBean) {
            /*var titleStr = ""
            when (mOrderType) {
                //转账
                CommonConstants.TransType.Transfer -> {
                    titleStr = getString(R.string.send_money)
                }

                CommonConstants.TransType.Query_BALANCE, CommonConstants.TransType.Query_Transfer -> {
                    titleStr = mRecordTitle
                }

                CommonConstants.TransType.Bill_PAY -> {
                    titleStr = payFeesTitleMap[bean.billType].toString()
                }

                CommonConstants.TransType.TopUp -> {
                    titleStr = getString(R.string.bank_to_OneMoney)
                }

                CommonConstants.TransType.Withdraw -> {
                    titleStr = getString(R.string.oneMoney_to_Bank)
                }

                CommonConstants.TransType.ZIPIT,CommonConstants.TransType.ZIPIT_TWO -> {
                    titleStr = getString(R.string.zipit_title)
                }

                CommonConstants.TransType.Payment -> {
                    if (CommonConstants.PaymentProduct.PAYMENT_THIRTY==bean.paymentProduct) {
                        titleStr = mRecordTitle
                    }else{
                        titleStr = getString(R.string.payment)
                    }
                }

                CommonConstants.TransType.Refund -> {
                    titleStr = getString(R.string.refund)
                }

                CommonConstants.TransType.Agent_Transfer -> {
                    titleStr = getString(R.string.agent_transfer)
                }

                else -> {
                    titleStr = mRecordTitle
                }

            }*/

            titleTv.text = tradeTypeName
            val iconId = TransferUiConstants.getIcon(mOrderType)
            val drawableLeft = ResourceHelper.getDrawable(iconId)
            drawableLeft?.setBounds(0, 0, 56.pt2px(), 56.pt2px())
            titleTv.setCompoundDrawables(drawableLeft, null, null, null);
            titleTv.compoundDrawablePadding = 25.pt2px()
        }

        private fun setMoneyAndStatus(mOrderType: String, rootView: View, bean: OrderDetailsBean) {
            val mMoneyTv = rootView.findViewById<TextView>(R.id.moneyTv)
            val mUsd = rootView.findViewById<TextView>(R.id.usd)
            val statusTv = rootView.findViewById<TextView>(R.id.statusTv)

            var tradeAmt: String = bean.tradeAmt
            var statusStr = ""

            when (mOrderType) {

                CommonConstants.TransType.Agent_Transfer -> {
                    val orderStatus = bean.orderStatus
                    tradeAmt = bean.orderIncomeFlag + StringUtils.formatAmount(tradeAmt)
                    if (TextUtils.equals(TransferConstants.OrderStatus.PENDING, orderStatus)) {
                        statusStr = getString(R.string.history_label_pending)
                    } else if (TextUtils.equals(TransferConstants.OrderStatus.SUCCESS,
                            orderStatus)
                    ) {
                        statusStr = getString(R.string.withdrawal_label_successful)
                    } else if (TextUtils.equals(TransferConstants.OrderStatus.FAIL, orderStatus)) {
                        statusStr = getString(R.string.history_label_failed)
                    } else if (TextUtils.equals(TransferConstants.OrderStatus.FULLREFUND,
                            orderStatus)
                    ) {
                        statusStr = getString(R.string.history_label_full_refund)
                    } else if (TextUtils.equals(TransferConstants.OrderStatus.PARTIALREFUND,
                            orderStatus)
                    ) {
                        statusStr = getString(R.string.history_label_partial_refund)
                    }
                }

                CommonConstants.TransType.Withdraw -> {
                    when (bean.orderStatus) {
                        TransferConstants.WithdrawalOrderStatus.SUCCESS -> {
                            statusStr = getString(R.string.withdrawal_label_successful)
                            tradeAmt = bean.orderIncomeFlag + StringUtils.formatAmount(tradeAmt)
                        }

                        TransferConstants.WithdrawalOrderStatus.FAIL -> {
                            statusStr = getString(R.string.failed)
                            tradeAmt = StringUtils.formatAmount(tradeAmt)
                        }
                    }
                }

                else -> {
                    when (bean.orderStatus) {
                        TransferConstants.OrderStatus.SUCCESS -> {
                            statusStr = getString(R.string.withdrawal_label_successful)
                            tradeAmt = bean.orderIncomeFlag + StringUtils.formatAmount(tradeAmt)
                        }

                        TransferConstants.OrderStatus.FAIL -> {
                            statusStr = getString(R.string.failed)
                            tradeAmt = bean.orderIncomeFlag + StringUtils.formatAmount(tradeAmt)
                        }

                        else -> {
                            statusStr = getString(R.string.withdrawal_label_successful)
                            tradeAmt = bean.orderIncomeFlag + StringUtils.formatAmount(tradeAmt)
                        }
                    }
                }
            }

            mUsd.text = CurrencyUtils.setCurrency(BaseApp.getAPPContext(), bean.orderCurrType)
            mMoneyTv.text = tradeAmt
            statusTv.text = statusStr
        }

        @JvmStatic
        fun getData(type: String, bean: OrderDetailsBean): ArrayList<HistoryDetailsItemBean>? {
            when (type) {
                //转账
                CommonConstants.TransType.Bulk_Payment -> return getFundTransferData(bean)
                CommonConstants.TransType.Transfer -> return getFundTransferData(bean)
                CommonConstants.TransType.Transfer_Reversal -> return getFundTransferReversalData(bean)
                CommonConstants.TransType.Agent_Transfer -> return getAgentTransferData(bean)
                // 生活缴费
                CommonConstants.TransType.Bill_PAY -> return getBillPayData(bean)
                CommonConstants.TransType.Mobile_Fee -> return getAirtimeData(bean)
                CommonConstants.TransType.Agent_Zesa -> return getAgentZesaData(bean)
                CommonConstants.TransType.Agent_MOBILLE -> return getAgentAirtimeData(bean)
                // 查询
                CommonConstants.TransType.Query_BALANCE, CommonConstants.TransType.Query_Transfer -> return getQueryData(
                    bean)
                // 充值
                CommonConstants.TransType.TopUp -> return getTopUpData(bean)
                // 提现
                CommonConstants.TransType.Withdraw -> return getWithdraw(bean)
                // 支付
                CommonConstants.TransType.Payment -> return getPayData(bean)
                CommonConstants.TransType.Refund -> return getRefundData(bean)
                // ZIPIT
                CommonConstants.TransType.ZIPIT,CommonConstants.TransType.ZIPIT_TWO -> return getZIPIT(bean)
                CommonConstants.TransType.ZIPIT_Reversal -> return getZIPITReversal(bean)

                // Thirty Party Paying
                CommonConstants.TransType.Thirty_Party_Paying -> return getThirty(bean)
            }
            return arrayListOf()
        }

        private fun getString(id: Int) = ResourceHelper.getString(id)

        private fun isValidMoney(amt: String) = MoneyUtil.isValidMoney(amt)

        @JvmStatic
        fun getAmt(amt: String, orderCurrType: String): String {
            val feeAmtBd = BigDecimal(amt)
            return CurrencyUtils.setCurrency(BaseApp.getAPPContext(),
                orderCurrType) + " " + feeAmtBd.setScale(2, BigDecimal.ROUND_HALF_UP)
        }

        @JvmStatic
        fun getContent(content: String) = if (content == "") "N/A" else content

        private fun getRefundData(bean: OrderDetailsBean) =
            arrayListOf<HistoryDetailsItemBean>().apply {
                if ("30".equals(bean.originalTradeType)){
                    add(HistoryDetailsItemBean(getString(R.string.merchant), bean.merName))
                    add(HistoryDetailsItemBean(getString(R.string.commodity), getString(R.string.commodity)))
                }else{
                    add(HistoryDetailsItemBean(getString(R.string.biller), bean.merName))
                    add(HistoryDetailsItemBean(getString(R.string.biller_Type), bean.billTypeDesc))
                }

                val refundStr = getString(R.string.refund) + " "
                val currency = bean.currency + " "
                if (isValidMoney(bean.refundPayFeeAmt)) add(HistoryDetailsItemBean(refundStr + getString(
                    R.string.fee), currency + bean.refundPayFeeAmt))
                if (isValidMoney(bean.refundPayTaxAmt)) add(HistoryDetailsItemBean(refundStr + getString(
                    R.string.tax), currency + bean.refundPayTaxAmt))
                add(HistoryDetailsItemBean(refundStr + getString(
                    R.string.account), bean.refundAccount))
                if (isValidMoney(bean.walletRefundAmount)) add(HistoryDetailsItemBean(getString(R.string.wallet_Account_Refund_Amount),
                    currency + bean.walletRefundAmount))
                if (isValidMoney(bean.cashbackRefundAmount)) add(HistoryDetailsItemBean(getString(R.string.cashBack_Account_Refund_Amount),
                    currency + bean.cashbackRefundAmount))

                add(HistoryDetailsItemBean(getString(R.string.date_time),
                    TimeUtils.getTime(bean.tradeTime)))
                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))
                add(HistoryDetailsItemBean(getString(R.string.related_Payment_Transaction_No),
                    bean.originalOrderNo))
                if (StringUtils.isValidString(bean.outOrderNo)) add(
                    HistoryDetailsItemBean(
                        getString(R.string.merchant_Order_No),
                        bean.outOrderNo
                    )
                )
            }


        private fun getBulkPayment(bean: OrderDetailsBean) =
            arrayListOf<HistoryDetailsItemBean>().apply {
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                add(HistoryDetailsItemBean(getString(R.string.merchant), bean.name))
                addOtherInfo(this,bean)
            }

        private fun getPayData(bean: OrderDetailsBean): ArrayList<HistoryDetailsItemBean>? {
            when (bean.paymentProduct) {
                CommonConstants.PaymentProduct.ORDER_QRCODE,
                CommonConstants.PaymentProduct.QRCODE,
                CommonConstants.PaymentProduct.APP,
                CommonConstants.PaymentProduct.BARCODE,
                -> {
                    return if (MoneyUtil.isValidMoney(bean.discountAmt)) {
                        getMarketingPayData(bean)
                    } else {
                        getNoMarketingPayData(bean)
                    }
                }
                CommonConstants.PaymentProduct.PAYMENT_THIRTY,
                -> {
                    return getPayThirtyData(bean)
                }
                else -> {
                    return getOtherPayData(bean)
                }
            }
        }

        private fun getPayThirtyData(bean: OrderDetailsBean) =
            arrayListOf<HistoryDetailsItemBean>().apply {
                if ("1"==bean.businessType){
                    add(HistoryDetailsItemBean(getString(R.string.business_bype), getString(R.string.business_bype_pay1)))
                }else{
                    add(HistoryDetailsItemBean(getString(R.string.business_bype), getString(R.string.business_bype_pay0)))
                }
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                add(HistoryDetailsItemBean(getString(R.string.merchant), bean.name))
                if (!TextUtils.isEmpty(bean.commodityRemark))
                {
                    add(HistoryDetailsItemBean(getString(R.string.commodity), bean.commodityRemark))
                }else{
                    add(HistoryDetailsItemBean(getString(R.string.commodity), getString(R.string.commodity)))
                }
                addTaxAndFee(this, bean)
                add(HistoryDetailsItemBean(getString(R.string.date_time), bean.tradeTime))
                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))
                if (StringUtils.isValidString(bean.outOrderNo)) add(HistoryDetailsItemBean(getString(
                    R.string.merchant_Order_No), bean.outOrderNo))
                //add(HistoryDetailsItemBean(getString(R.string.trading_source), bean.tradingSource))
            }

        private fun getNoMarketingPayData(bean: OrderDetailsBean) =
            arrayListOf<HistoryDetailsItemBean>().apply {
                add(HistoryDetailsItemBean(getString(R.string.merchant), bean.name))
                if (!TextUtils.isEmpty(bean.commodityRemark))
                {
                    add(HistoryDetailsItemBean(getString(R.string.commodity), bean.commodityRemark))
                }else{
                    add(HistoryDetailsItemBean(getString(R.string.commodity), getString(R.string.commodity)))
                }
                addTaxAndFee(this, bean)
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                add(HistoryDetailsItemBean(getString(R.string.transaction_Mode),
                    bean.transactionMode))
                add(HistoryDetailsItemBean(getString(R.string.date_time), bean.tradeTime))

                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))
                if (StringUtils.isValidString(bean.outOrderNo)) add(HistoryDetailsItemBean(getString(
                    R.string.merchant_Order_No), bean.outOrderNo))
            }

        private fun getMarketingPayData(bean: OrderDetailsBean) =
            arrayListOf<HistoryDetailsItemBean>().apply {
                add(HistoryDetailsItemBean(getString(R.string.merchant), bean.name))
                if (!TextUtils.isEmpty(bean.commodityRemark))
                {
                    add(HistoryDetailsItemBean(getString(R.string.commodity), bean.commodityRemark))
                }else{
                    add(HistoryDetailsItemBean(getString(R.string.commodity), getString(R.string.commodity)))
                }

                val currStr = bean.orderCurrType + " "
                val currStr_ = bean.orderCurrType + " -"

                val discountAmt = bean.discountAmt
                if (MoneyUtil.isValidMoney(discountAmt)) {
                    val couponAmt =
                        if (StringUtils.isValidString(bean.couponAmt)) currStr_ + bean.couponAmt else ""
                    val membershipPointAmt =
                        if (StringUtils.isValidString(bean.membershipPointAmt)) currStr_ + bean.membershipPointAmt else ""
                    val membershipBonusAmt =
                        if (StringUtils.isValidString(bean.membershipBonusAmt)) currStr_ + bean.membershipBonusAmt else ""
                    add(HistoryDetailsItemBean(getString(R.string.discount_Amount),
                        currStr_ + discountAmt,
                        type = 1,
                        DiscountBean(couponAmt, membershipPointAmt, membershipBonusAmt)))
                }
                addTaxAndFee(this, bean)
                if (StringUtils.isValidString(bean.actualAmt)) {
                    if (bean.payMethod.contains(getString(R.string.bank))){
                        add(HistoryDetailsItemBean(getString(R.string.bank_Account_Payment_Amount), currStr + bean.actualAmt))
                    }else{
                        add(HistoryDetailsItemBean(getString(R.string.wallet_Account_Payment_Amount), currStr + bean.actualAmt))
                    }

                }
                if (StringUtils.isValidString(bean.cashbackAmt)) add(HistoryDetailsItemBean(
                    getString(R.string.cashBack_Account_Payment_Amount),
                    currStr + bean.cashbackAmt))

                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                add(HistoryDetailsItemBean(getString(R.string.transaction_Mode),
                    bean.transactionMode))
                add(HistoryDetailsItemBean(getString(R.string.date_time), bean.tradeTime))

                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))
                if (StringUtils.isValidString(bean.outOrderNo)) add(HistoryDetailsItemBean(getString(
                    R.string.merchant_Order_No), bean.outOrderNo))
            }

        private fun getOtherPayData(bean: OrderDetailsBean) =
            arrayListOf<HistoryDetailsItemBean>().apply {
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                add(HistoryDetailsItemBean(getString(R.string.transaction_Mode),
                    bean.transactionMode))
                add(HistoryDetailsItemBean(getString(R.string.merchant), bean.name))
                if (!TextUtils.isEmpty(bean.commodityRemark))
                {
                    add(HistoryDetailsItemBean(getString(R.string.commodity), bean.commodityRemark))
                }else{
                    add(HistoryDetailsItemBean(getString(R.string.commodity), getString(R.string.commodity)))
                }
                val feeAmt = bean.payFeeAmt
                if (MoneyUtil.isValidMoney(feeAmt)) {
                    add(HistoryDetailsItemBean(getString(R.string.fee),
                        getAmt(feeAmt, bean.orderCurrType)))
                }

                val taxAmt = bean.payTaxAmt
                if (MoneyUtil.isValidMoney(taxAmt)) {
                    add(HistoryDetailsItemBean(getString(R.string.tax),
                        getAmt(taxAmt, bean.orderCurrType)))
                }
                add(HistoryDetailsItemBean(getString(R.string.date_time), bean.tradeTime))

                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))
                if (StringUtils.isValidString(bean.outOrderNo)) add(HistoryDetailsItemBean(getString(
                    R.string.merchant_Order_No), bean.outOrderNo))

//            add(HistoryDetailsItemBean(getString(R.string.trading_source), bean.tradingSource))
            }

        fun getTopUpData(bean: OrderDetailsBean) = arrayListOf<HistoryDetailsItemBean>().apply {
            add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
            add(HistoryDetailsItemBean(getString(R.string.account),
                bean.orderCurrType + " " + getString(R.string.account)))
            add(HistoryDetailsItemBean(getString(R.string.payment_Account),
                bean.payMethod))

            add(HistoryDetailsItemBean(getString(R.string.date_time),
                TimeUtils.getTime(bean.tradeTime)))

            add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))

            if (bean.orderStatus == "2") {
                add(HistoryDetailsItemBean(getString(R.string.failed_Reason), bean.resultRemark))
            }
        }

        fun getZIPIT(bean: OrderDetailsBean) = arrayListOf<HistoryDetailsItemBean>().apply {
            add(HistoryDetailsItemBean(getString(R.string.bank_Account),
                bean.bankName +"("+ bean.bankCardNo+")"))
            add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
            addTaxAndFee(this,bean)
            add(HistoryDetailsItemBean(getString(R.string.date_time),
                TimeUtils.getTime(bean.completeTime)))

            add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))

            //add(HistoryDetailsItemBean(getString(R.string.trading_source), bean.tradingSource))
            if (bean.orderStatus == "2") {
                add(HistoryDetailsItemBean(getString(R.string.failed_Reason), bean.resultRemark))
            }
            val remark = bean.remark
            if (StringUtils.isValidString(remark)){
                add(HistoryDetailsItemBean(getString(R.string.common_label_sender), remark))
            }
        }

        fun getZIPITReversal(bean: OrderDetailsBean) = arrayListOf<HistoryDetailsItemBean>().apply {
            add(HistoryDetailsItemBean(getString(R.string.bank_Name), bean.bankName))
            add(HistoryDetailsItemBean(getString(R.string.bank_Account), bean.cardNo))
            add(HistoryDetailsItemBean(getString(R.string.payment_Account), bean.payMethod))
            //addTaxAndFee(this,bean)
            add(HistoryDetailsItemBean(getString(R.string.date_time), TimeUtils.getTime(bean.createTime)))

            add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))

            add(HistoryDetailsItemBean(getString(R.string.original_transaction), bean.originalOrderNo))
        }

        fun getThirty(bean: OrderDetailsBean) = arrayListOf<HistoryDetailsItemBean>().apply {
            if ("1"==bean.businessType){
                add(HistoryDetailsItemBean(getString(R.string.business_bype), getString(R.string.business_bype1)))
            }else{
                add(HistoryDetailsItemBean(getString(R.string.business_bype), getString(R.string.business_bype0)))
            }

            add(HistoryDetailsItemBean(getString(R.string.merchant), bean.name))
            if (!TextUtils.isEmpty(bean.commodityRemark))
            {
                add(HistoryDetailsItemBean(getString(R.string.commodity), bean.commodityRemark))
            }else{
                add(HistoryDetailsItemBean(getString(R.string.commodity), getString(R.string.commodity)))
            }
            //addTaxAndFee(this,bean)
            //add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
            add(HistoryDetailsItemBean(getString(R.string.date_time), TimeUtils.getTime(bean.tradeTime)))
            add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))
            add(HistoryDetailsItemBean(getString(R.string.merchant_Order_No), bean.outOrderNo))
            //add(HistoryDetailsItemBean(getString(R.string.trading_source), bean.tradingSource))
        }

        fun getWithdraw(bean: OrderDetailsBean) = arrayListOf<HistoryDetailsItemBean>().apply {
            add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
            add(HistoryDetailsItemBean(getString(R.string.bank_Account),
                bean.bankName +"("+ bean.bankCardNo+")"))
            add(HistoryDetailsItemBean(getString(R.string.date_time),
                TimeUtils.getTime(bean.completeTime)))

            add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))

            //add(HistoryDetailsItemBean(getString(R.string.trading_source), bean.c))
            if (bean.orderStatus == "2") {
                add(HistoryDetailsItemBean(getString(R.string.failed_Reason), bean.resultRemark))
            }
        }

        private fun getQueryData(bean: OrderDetailsBean) =
            arrayListOf<HistoryDetailsItemBean>().apply {
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                add(HistoryDetailsItemBean(getString(R.string.account), bean.enquiryAccount))
                addTaxAndFee(this, bean)
                add(HistoryDetailsItemBean(getString(R.string.date_time),
                    TimeUtils.getTime(bean.tradeTime)))
                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))
            }

        private fun getAgentTransferData(bean: OrderDetailsBean) =
            arrayListOf<HistoryDetailsItemBean>().apply {
                val isPaid = "-" == bean.orderIncomeFlag
                val mobileTitle = getString(if (isPaid) R.string.payee_mobile else R.string.payer_mobile)
                val nameTitle = getString(if (isPaid) R.string.payee_name else R.string.payer_name)
                add(HistoryDetailsItemBean(mobileTitle, bean.counterpartyMobileNo))
                add(HistoryDetailsItemBean(nameTitle, bean.counterpartyName))

                addTaxAndFee(this, bean)
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                add(HistoryDetailsItemBean(getString(R.string.branch_name), bean.agentName))
                addOtherInfo(this, bean)
            }

        private fun getFundTransferData(bean: OrderDetailsBean): ArrayList<HistoryDetailsItemBean> {
            return arrayListOf<HistoryDetailsItemBean>().apply {
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                val isPaid = "-" == bean.orderIncomeFlag

                val mobile = if (isPaid) bean.recMobile else bean.payMobile
                val mobileTitle = getString(if (isPaid) R.string.payee_mobile else R.string.payer_mobile)
                val nameTitle = getString(if (isPaid) R.string.payee_name else R.string.payer_name)
                if (bean.payCstType!="0") {
                    add(HistoryDetailsItemBean(mobileTitle, mobile))
                }
                    add(HistoryDetailsItemBean(nameTitle, bean.name))
                if (isPaid) {
                    val feeAmt = bean.payFeeAmt
                    if (MoneyUtil.isValidMoney(feeAmt)) {
                        add(HistoryDetailsItemBean(getString(R.string.fee),
                            getAmt(feeAmt, bean.orderCurrType)))
                    }

                    val taxAmt = bean.taxAmt
                    if (MoneyUtil.isValidMoney(feeAmt)) {
                        add(HistoryDetailsItemBean(getString(R.string.tax),
                            getAmt(taxAmt, bean.orderCurrType)))
                    }
                }
                addOtherInfo(this, bean)
            }
        }

        private fun getFundTransferReversalData(bean: OrderDetailsBean): ArrayList<HistoryDetailsItemBean> {
            return arrayListOf<HistoryDetailsItemBean>().apply {
                add(HistoryDetailsItemBean(getString(R.string.payer_name), bean.name))
                add(HistoryDetailsItemBean(getString(R.string.payment_Account), bean.payMethod))
                add(HistoryDetailsItemBean(getString(R.string.date_time), TimeUtils.getTime(bean.tradeTime)))
                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))
                add(HistoryDetailsItemBean(getString(R.string.original_transaction), bean.originalOrderId))
                val remark = bean.remark
                if (StringUtils.isValidString(remark)) add(HistoryDetailsItemBean(getString(R.string.remark),
                    remark))
            }
        }

        private fun getMerSendMoneyData(bean: OrderDetailsBean) =
            arrayListOf<HistoryDetailsItemBean>().apply {
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                add(HistoryDetailsItemBean(getString(R.string.merchant), ""))
                add(HistoryDetailsItemBean(getString(R.string.date_time),
                    TimeUtils.getTime(bean.tradeTime)))
                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))
                add(HistoryDetailsItemBean(getString(R.string.remark), getContent(bean.remark)))
            }

        private fun getAgentAirtimeData(bean: OrderDetailsBean) =
            arrayListOf<HistoryDetailsItemBean>().apply {
                add(HistoryDetailsItemBean(getString(R.string.mobile_No), bean.recMobileNo))
                add(HistoryDetailsItemBean(getString(R.string.transaction_Type),
                    bean.rechargeTradingType))
                if (StringUtils.isValidString(bean.bundleName)) {
                    add(HistoryDetailsItemBean(getString(R.string.bundle_Name), bean.bundleName))
                    add(HistoryDetailsItemBean(getString(R.string.bundle_Plan), bean.bundlePlan))
                } else {
                    add(HistoryDetailsItemBean(getString(R.string.airtime),
                        bean.currency + " " + bean.tradeAmt))
                }
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                add(HistoryDetailsItemBean(getString(R.string.branch_name), bean.agentName))
                //add(HistoryDetailsItemBean(getString(R.string.operator), bean.agentOperName))
                add(HistoryDetailsItemBean(getString(R.string.date_time), bean.tradeTime))
                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))

            }

        private fun getAgentZesaData(bean: OrderDetailsBean) =
            arrayListOf<HistoryDetailsItemBean>().apply {
                add(HistoryDetailsItemBean(getString(R.string.meter_Number), bean.meterNo))
                add(HistoryDetailsItemBean(getString(R.string.user_Name), bean.meterName))
                addTaxAndFee(this, bean)
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                add(HistoryDetailsItemBean(getString(R.string.branch_name), bean.agentName))
                addOtherInfo(this, bean)
            }

        private fun getAirtimeData(bean: OrderDetailsBean): java.util.ArrayList<HistoryDetailsItemBean> {
            return arrayListOf<HistoryDetailsItemBean>().apply {
                add(HistoryDetailsItemBean(getString(R.string.mobile_No), bean.billAccount))
                add(HistoryDetailsItemBean(getString(R.string.transaction_Type),
                    bean.rechargeTradingType))
                if (StringUtils.isValidString(bean.bundleName)) {
                    add(HistoryDetailsItemBean(getString(R.string.bundle_Name), bean.bundleName))
                    add(HistoryDetailsItemBean(getString(R.string.bundle_Plan), bean.bundlePlan))
                } else {
                    add(HistoryDetailsItemBean(getString(R.string.airtime),
                        bean.currency + " " + bean.tradeAmt))
                }
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                val discountAmt = bean.discountAmt
                val currStr_ = bean.orderCurrType + " -"
                if (MoneyUtil.isValidMoney(discountAmt)) {
                    val couponAmt =
                        if (StringUtils.isValidString(bean.couponAmt)) currStr_ + bean.couponAmt else ""
                    val membershipPointAmt =
                        if (StringUtils.isValidString(bean.membershipPointAmt)) currStr_ + bean.membershipPointAmt else ""
                    val membershipBonusAmt =
                        if (StringUtils.isValidString(bean.membershipBonusAmt)) currStr_ + bean.membershipBonusAmt else ""
                    add(HistoryDetailsItemBean(getString(R.string.discount_Amount),
                        currStr_ + discountAmt,
                        type = 1,
                        DiscountBean(couponAmt, membershipPointAmt, membershipBonusAmt)))
                }
                val actualAmt = bean.actualAmt
                if (MoneyUtil.isValidMoney(actualAmt)) {
                        if (bean.payMethod.contains(getString(R.string.bank))){
                            add(HistoryDetailsItemBean(getString(R.string.bank_Account_Payment_Amount), bean.currency + " " + actualAmt))
                        }else{
                            add(HistoryDetailsItemBean(getString(R.string.wallet_Account_Payment_Amount), bean.currency + " " + actualAmt))
                        }
                    }
                val cashbackAmt = bean.cashbackAmt
                if (MoneyUtil.isValidMoney(cashbackAmt))
                {
                    add(HistoryDetailsItemBean(getString(R.string.cashBack_Account_Payment_Amount),
                        bean.currency + " " + cashbackAmt))
                }
                add(HistoryDetailsItemBean(getString(R.string.date_time), bean.tradeTime))
                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))
                add(HistoryDetailsItemBean(getString(R.string.channel_no), bean.channelTransNo))
            }
        }

        private fun getBillPayData(bean: OrderDetailsBean): java.util.ArrayList<HistoryDetailsItemBean> {
            return arrayListOf<HistoryDetailsItemBean>().apply {
                if (bean.billTypeDesc==null){
                    add(HistoryDetailsItemBean(getString(R.string.biller_Type), bean.billType))
                }else{
                    add(HistoryDetailsItemBean(getString(R.string.biller_Type), bean.billTypeDesc))
                }

                when (bean.billType) {
//                        *         01：哈拉雷市政
                    HARARE -> {
                        add(HistoryDetailsItemBean(getString(R.string.account_of_City_of_Harare),
                            bean.billAccount))
                    }
//                        *         02：电费
                    ELE_FEES -> {
                        add(HistoryDetailsItemBean(getString(R.string.meter_Number),
                            bean.billAccount))
                        add(HistoryDetailsItemBean(getString(R.string.user_Name), bean.meterName))
                    }
//                        *         03：学费
                    SCHOOL_FEES -> {
                        add(HistoryDetailsItemBean(getString(R.string.School_name), bean.billerName))
                        var semester = ""
                        when (bean.semester) {
                            "1" -> {
                                semester = getString(R.string.first_term)
                            }
                            "2" -> {
                                semester = getString(R.string.second_term)
                            }
                            "3" -> {
                                semester = getString(R.string.third_term)
                            }
                        }
                        add(HistoryDetailsItemBean(getString(R.string.School_term), semester))
                        add(HistoryDetailsItemBean(getString(R.string.Students_Class), bean.classes))
                        add(HistoryDetailsItemBean(getString(R.string.Students_Name), bean.billAccount))
                    }
//                        *         04：党费
                    PARTY_FEES -> {
                        add(HistoryDetailsItemBean(getString(R.string.membership_Number),
                            bean.billAccount))
                    }
//                        *         05：社会福利
                    SOCIAL_WELFARE -> {
                        add(HistoryDetailsItemBean(getString(R.string.Reason_of_Payment),
                            bean.billPaymentType))
                        add(HistoryDetailsItemBean(getString(R.string.owner_name), bean.userName))
                        add(HistoryDetailsItemBean(getString(R.string.address), bean.address))
                    }
//                        *         06：其他
                    OTHER -> {
                        add(HistoryDetailsItemBean(getString(R.string.biller_Name),
                            bean.billerName))
                        add(HistoryDetailsItemBean(getString(R.string.account_no),
                            bean.billAccount))
                    }
                }
                addTaxAndFee(this, bean)
                add(HistoryDetailsItemBean(getString(R.string.payment_method), bean.payMethod))
                /*val discountAmt = bean.discountAmt
                val currStr_ = bean.orderCurrType + " - "
                if (MoneyUtil.isValidMoney(discountAmt)) {
                    val couponAmt =
                        if (StringUtils.isValidString(bean.couponAmt)) currStr_ + bean.couponAmt else ""
                    val membershipPointAmt =
                        if (StringUtils.isValidString(bean.membershipPointAmt)) currStr_ + bean.membershipPointAmt else ""
                    val membershipBonusAmt =
                        if (StringUtils.isValidString(bean.membershipBonusAmt)) currStr_ + bean.membershipBonusAmt else ""
                    add(HistoryDetailsItemBean(getString(R.string.discount_Amount),
                        currStr_ + discountAmt,
                        type = 1,
                        DiscountBean(couponAmt, membershipPointAmt, membershipBonusAmt)))
                }
                val actualAmt = bean.actualAmt
                if (MoneyUtil.isValidMoney(actualAmt)) {
                    if ("03" == bean.paymentType){
                        add(HistoryDetailsItemBean(getString(R.string.bank_Account_Payment_Amount), bean.currency + " " + actualAmt))
                    }else{
                        add(HistoryDetailsItemBean(getString(R.string.wallet_Account_Payment_Amount), bean.currency + " " + actualAmt))
                    }
                }
                val cashbackAmt = bean.cashbackAmt
                if (MoneyUtil.isValidMoney(cashbackAmt))
                {
                    add(HistoryDetailsItemBean(getString(R.string.cashBack_Account_Payment_Amount),
                        bean.currency + " " + cashbackAmt))
                }*/

                add(HistoryDetailsItemBean(getString(R.string.date_time),
                    TimeUtils.getTime(bean.tradeTime)))
                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))
            }
        }

        private fun addTaxAndFee(list: java.util.ArrayList<HistoryDetailsItemBean>,
            bean: OrderDetailsBean) {
            list.apply {
                var feeAmt = if (CommonConstants.TransType.Payment==bean.tradeType)
                    bean.payFeeAmt else
                    bean.feeAmt

                if (feeAmt!=null&&MoneyUtil.isValidMoney(feeAmt)) {
                    add(HistoryDetailsItemBean(getString(R.string.fee),
                        getAmt(feeAmt, bean.orderCurrType)))
                }

                val taxAmt = bean.taxAmt
                if (taxAmt!=null&&MoneyUtil.isValidMoney(taxAmt)) {
                    add(HistoryDetailsItemBean(getString(R.string.tax),
                        getAmt(taxAmt, bean.orderCurrType)))
                }
            }
        }

        private fun addOtherInfo(list: java.util.ArrayList<HistoryDetailsItemBean>,
            bean: OrderDetailsBean,
            type: String = "") {
            list.apply {
                add(HistoryDetailsItemBean(getString(R.string.date_time),
                    TimeUtils.getTime(bean.tradeTime)))
                add(HistoryDetailsItemBean(getString(R.string.transaction_no), bean.orderId))

                val remark = bean.remark
                if (StringUtils.isValidString(remark)) add(HistoryDetailsItemBean(getString(R.string.remark),
                    remark))
            }
        }
    }
}