<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
            android:id="@+id/include_title"
            layout="@layout/transfer_base_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/amount_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30pt"
                android:layout_marginTop="25pt"
                android:layout_marginRight="30pt"
                android:text="@string/amount"
                android:textSize="28pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/bg_view"
                cornerBackgroundRadius="@{10}"
                android:layout_width="match_parent"
                android:layout_height="100pt"
                android:layout_marginLeft="30pt"
                android:layout_marginTop="12pt"
                android:layout_marginRight="30pt"
                app:layout_constraintTop_toBottomOf="@id/amount_tv" />

            <TextView
                android:id="@+id/currency_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28pt"
                android:text="@string/zwl"
                android:textColor="@color/common_ye_F3881E"
                android:textSize="32pt"
                app:layout_constraintBottom_toBottomOf="@id/bg_view"
                app:layout_constraintLeft_toLeftOf="@id/bg_view"
                app:layout_constraintTop_toTopOf="@id/bg_view" />

            <View
                android:id="@+id/divider_view"
                android:layout_width="1pt"
                android:layout_height="0pt"
                android:layout_marginLeft="28pt"
                android:layout_marginTop="20pt"
                android:layout_marginRight="28pt"
                android:layout_marginBottom="20pt"
                android:background="@color/color_E5E6EB"
                app:layout_constraintBottom_toBottomOf="@id/bg_view"
                app:layout_constraintLeft_toRightOf="@id/currency_tv"
                app:layout_constraintTop_toTopOf="@id/bg_view" />

            <EditText
                android:id="@+id/moneyTv"
                android:layout_width="0pt"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28pt"
                android:layout_marginRight="28pt"
                android:background="@null"
                android:digits="0123456789."
                android:gravity="left|bottom"
                android:hint="@string/common_label_0_00"
                android:inputType="number"
                android:textColor="@color/common_text_1d2129"
                android:textCursorDrawable="@drawable/common_cursor"
                android:textSize="32pt"
                app:layout_constraintBottom_toBottomOf="@id/bg_view"
                app:layout_constraintLeft_toRightOf="@id/divider_view"
                app:layout_constraintRight_toRightOf="@id/bg_view"
                app:layout_constraintTop_toTopOf="@id/bg_view" />

            <TextView
                android:id="@+id/bankName_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30pt"
                android:layout_marginTop="25pt"
                android:layout_marginRight="30pt"
                android:text="@string/bank_Name"
                android:textSize="28pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/bg_view" />

            <View
                android:id="@+id/bg_view1"
                cornerBackgroundRadius="@{10}"
                android:layout_width="match_parent"
                android:layout_height="100pt"
                android:layout_marginLeft="30pt"
                android:layout_marginTop="12pt"
                android:layout_marginRight="30pt"
                app:layout_constraintTop_toBottomOf="@id/bankName_title" />

            <TextView
                android:id="@+id/bankName_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28pt"
                android:textColor="@color/common_text_1d2129"
                android:textSize="32pt"
                app:layout_constraintBottom_toBottomOf="@id/bg_view1"
                app:layout_constraintLeft_toLeftOf="@id/bg_view1"
                app:layout_constraintTop_toTopOf="@id/bg_view1" />

            <ImageView
                android:id="@+id/selectBank_iv"
                android:layout_width="48pt"
                android:layout_height="48pt"
                android:layout_marginRight="24pt"
                android:background="@drawable/common_ic_next"
                app:layout_constraintBottom_toBottomOf="@id/bg_view1"
                app:layout_constraintRight_toRightOf="@id/bg_view1"
                app:layout_constraintTop_toTopOf="@id/bg_view1" />

            <TextView
                android:id="@+id/bankAccount_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30pt"
                android:layout_marginTop="25pt"
                android:layout_marginRight="30pt"
                android:text="@string/bank_Account"
                android:textSize="28pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/bg_view1" />

            <androidx.cardview.widget.CardView
                android:id="@+id/account_card"
                android:layout_width="match_parent"
                android:layout_height="100pt"
                android:layout_marginLeft="30pt"
                android:layout_marginTop="12pt"
                android:layout_marginRight="30pt"
                app:cardBackgroundColor="@color/color_FFFFFF"
                app:cardCornerRadius="10pt"
                app:cardElevation="0pt"
                app:layout_constraintTop_toBottomOf="@id/bankAccount_title">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/bg_view2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <EditText
                        android:id="@+id/bankAccount_ed"
                        android:layout_width="0pt"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="28pt"
                        android:layout_marginRight="10pt"
                        android:background="@null"
                        android:gravity="left"
                        android:inputType="number"
                        android:maxLength="30"
                        android:textColor="@color/common_text_1d2129"
                        android:textCursorDrawable="@drawable/common_cursor"
                        android:textSize="32pt"
                        app:layout_constraintBottom_toBottomOf="@id/bg_view2"
                        app:layout_constraintLeft_toLeftOf="@id/bg_view2"
                        app:layout_constraintRight_toLeftOf="@id/textClear_iv"
                        app:layout_constraintTop_toTopOf="@id/bg_view2" />

                    <ImageView
                        android:id="@+id/textClear_iv"
                        android:layout_width="56pt"
                        android:layout_height="56pt"
                        android:layout_marginRight="24pt"
                        android:background="@drawable/ic_text_clear"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/bg_view2"
                        app:layout_constraintRight_toRightOf="@id/bg_view2"
                        app:layout_constraintTop_toTopOf="@id/bg_view2" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/remark_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30pt"
                android:layout_marginTop="25pt"
                android:layout_marginRight="30pt"
                android:text="@string/common_label_sender"
                android:textSize="28pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/account_card" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bg_view3"
                cornerBackgroundRadius="@{10}"
                android:layout_width="match_parent"
                android:layout_height="100pt"
                android:layout_marginLeft="30pt"
                android:layout_marginTop="12pt"
                android:layout_marginRight="30pt"
                app:layout_constraintTop_toBottomOf="@id/remark_title">

                <EditText
                    android:id="@+id/remark_ed"
                    android:layout_width="0pt"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="28pt"
                    android:layout_marginRight="10pt"
                    android:background="@null"
                    android:gravity="left"
                    android:maxLength="200"
                    android:textColor="@color/common_text_1d2129"
                    android:textCursorDrawable="@drawable/common_cursor"
                    android:textSize="32pt"
                    android:hint="@string/email_optional"
                    app:layout_constraintBottom_toBottomOf="@id/bg_view3"
                    app:layout_constraintLeft_toLeftOf="@id/bg_view3"
                    app:layout_constraintRight_toLeftOf="@id/remarkClear_iv"
                    app:layout_constraintTop_toTopOf="@id/bg_view3" />

                <ImageView
                    android:id="@+id/remarkClear_iv"
                    android:layout_width="56pt"
                    android:layout_height="56pt"
                    android:layout_marginRight="24pt"
                    android:background="@drawable/ic_text_clear"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/bg_view3"
                    app:layout_constraintRight_toRightOf="@id/bg_view3"
                    app:layout_constraintTop_toTopOf="@id/bg_view3" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bankView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30pt"
                android:layout_marginRight="30pt"
                android:background="@drawable/common_white_corner_10_bg"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/account_card">

                <TextView
                    android:id="@+id/hint_Tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24pt"
                    android:layout_marginTop="23pt"
                    android:gravity="left"
                    android:text="@string/there_are_linked_bank_accounts_available"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv"
                    android:layout_width="match_parent"
                    android:layout_height="352pt"
                    android:layout_marginTop="16pt"
                    app:layout_constraintTop_toBottomOf="@id/hint_Tv" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/nextTv"
                android:layout_width="match_parent"
                android:layout_height="80pt"
                android:layout_marginLeft="30pt"
                android:layout_marginTop="100pt"
                android:layout_marginRight="30pt"
                android:background="@drawable/common_unusable_btn"
                android:enabled="false"
                android:gravity="center"
                android:text="@string/check_account"
                android:textColor="@color/common_text_FFFFFF"
                android:textSize="32pt"
                app:layout_constraintTop_toBottomOf="@id/bg_view3" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

</layout>