package om.rrtx.mobile.securitymodule.model;

import android.text.TextUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean;
import om.rrtx.mobile.rrtxcommon1.bean.LoginBean;
import om.rrtx.mobile.rrtxcommon1.bean.RegisterInfoBean;
import om.rrtx.mobile.rrtxcommon1.bean.RegisterResultBean;
import om.rrtx.mobile.rrtxcommon1.bean.UpgradeInfoBean;
import om.rrtx.mobile.rrtxcommon1.model.CommonModel;
import om.rrtx.mobile.rrtxcommon1.net.BaseLoader;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitServiceManager;
import om.rrtx.mobile.securitymodule.SecurityConstants;
import om.rrtx.mobile.securitymodule.SecurityService;
import om.rrtx.mobile.securitymodule.bean.AuthorizationBean;
import om.rrtx.mobile.securitymodule.bean.AuthorizationListBean;
import om.rrtx.mobile.securitymodule.bean.AutoDebitInfoBean;
import om.rrtx.mobile.securitymodule.bean.AutoDebitResultBean;
import om.rrtx.mobile.securitymodule.bean.CodeBean;
import om.rrtx.mobile.securitymodule.bean.CurrencyLimitListBean;
import om.rrtx.mobile.securitymodule.bean.DeviceBean;
import om.rrtx.mobile.securitymodule.bean.OpenNoPinListBean;
import om.rrtx.mobile.securitymodule.bean.PayAccountListBean;
import om.rrtx.mobile.securitymodule.bean.PubBean;
import om.rrtx.mobile.securitymodule.bean.SdkInfoBean;
import om.rrtx.mobile.securitymodule.bean.SignListBean;
import om.rrtx.mobile.securitymodule.bean.UpDataBean;

/**
 * <AUTHOR>
 * 登录模块的整天请求
 */
public class SecurityModel extends BaseLoader {

    private SecurityService mSecurityService;

    public SecurityModel() {
        mSecurityService = RetrofitServiceManager.getInstance().create(SecurityService.class);
    }

    /**
     * 检查更新
     *
     * @param versionName
     * @param baseObserver 回调
     */
    public void requestUpData(String userId, String versionName, BaseNoDialogObserver<UpDataBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.OSTYPE, "1");
        map.put(SecurityConstants.Parameter.VERSIONNO, String.valueOf(versionName));
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestUpData(map)).subscribe(baseObserver);
    }

    public void commonPub(BaseObserver<PubBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mSecurityService.requestPub(map)).subscribe(baseObserver);
    }

    /**
     * 修改密码接口
     *
     * @param oldPsd       老密码
     * @param newPsd       新密码
     * @param baseObserver 回调)
     */
    public void requestPsdChange(String userId, String oldPsd, String newPsd, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.OLDPASSWORD, oldPsd);
        map.put(SecurityConstants.Parameter.NEWPASSWORD, newPsd);
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestPsdChange(map)).subscribe(baseObserver);
    }

    public void requestForgeOrSetPIN(String secretWord,String password, String userName, String idCard, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        /*if (!TextUtils.isEmpty(secretWord)) {
            map.put(UserConstants.Parameter.SECRETWORD, secretWord);
        }*/
        map.put(UserConstants.Parameter.PASSWORD, password);
        if (!TextUtils.isEmpty(userName)) {
            map.put(UserConstants.Parameter.USERNAME, userName);
        }
        map.put(UserConstants.Parameter.IDCARD, idCard);
        observe(mSecurityService.requestForgeOrSetPIN(map)).subscribe(baseObserver);
    }

    public void requestSetJuniorPIN(String juniorUserId, String pin, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.JUNIOR_USER_ID, juniorUserId);
        map.put(UserConstants.Parameter.PIN, pin);
        observe(mSecurityService.requestSetJuniorPIN(map)).subscribe(baseObserver);
    }

    public void requestDeleteJunior(String juniorId, String juniorMobile, String pin, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.JUNIOR_USER_ID, juniorId);
        map.put(UserConstants.Parameter.MOBILE, juniorMobile);
        map.put(UserConstants.Parameter.PIN, pin);
        observe(mSecurityService.requestDeleteJunior(map)).subscribe(baseObserver);
    }


    /**
     * 请求验证Pin码
     *
     * @param paymentPassword Pin码
     * @param baseObserver    回调
     */
    public void requestValidatePin(String userId, String paymentPassword, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.PIN, paymentPassword);
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestValidatePin(map)).subscribe(baseObserver);
    }

    /**
     * 验证安全问题
     *
     * @param secretWord Pin码
     * @param baseObserver    回调
     */
    public void requestValidateQUES(String secretWord,String userName, String idCard,BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.SECRETWORD, secretWord);
        //map.put(UserConstants.Parameter.PASSWORD, password);
        map.put(UserConstants.Parameter.USERNAME, userName);
        map.put(UserConstants.Parameter.IDCARD, idCard);
        observe(mSecurityService.requestValidateQUES(map)).subscribe(baseObserver);
    }

    /**
     * 请求设置Pin码
     *
     * @param paymentPassword Pin码
     * @param baseObserver    回调
     */
    public void requestSetPin(String userId, String paymentPassword, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.PAYMENTPASSWORD, paymentPassword);
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestSetPin(map)).subscribe(baseObserver);
    }

    /**
     * 请求修改Pin码
     *
     * @param oldPaymentPassword 老密码
     * @param paymentPassword    Pin码
     * @param baseObserver       回调
     */
    public void requestChangePin(String userId, String oldPaymentPassword, String paymentPassword, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.OLDPAYMENTPASSWORD, oldPaymentPassword);
        map.put(SecurityConstants.Parameter.NEWPAYMENTPASSWORD, paymentPassword);
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestChangePin(map)).subscribe(baseObserver);
    }

    /**
     * 获取验证码接口
     */
    public void requestCode(String mobile, String messageTemplateType, String mobileAreaCode, BaseObserver<CodeBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put("mobile", mobile);
        map.put("messageTemplateType", messageTemplateType);
        if (mobileAreaCode.equals("")) {
            mobileAreaCode = "263";
        }
        map.put("mobileAreaCode", mobileAreaCode);
        observe(mSecurityService.requestCode(map)).subscribe(baseObserver);
    }

    /**
     * 校验验证码接口
     */
    public void requestValidateSms(String userId, String mobile, String smsCode, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.MOBILE, mobile);
        map.put(SecurityConstants.Parameter.SMSCODE, smsCode);
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestValidateSms(map)).subscribe(baseObserver);
    }

    /**
     * 登录锁状态控制
     *
     * @param bioToken  敏感数据加密 bioStatus为1时有值
     * @param bioStatus 生物锁是否开通 0:开通 1开通
     */
    public void requestLoginLock(String userId, String bioToken, String bioStatus, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.BIOTOKEN, bioToken);
        map.put(SecurityConstants.Parameter.BIOSTATUS, bioStatus);
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestLoginLock(map)).subscribe(baseObserver);
    }

    /**
     * 请求登录接口
     *
     * @param userName       用户名
     * @param password       密码
     * @param registrationId
     * @param baseObserver   回调
     */
    public void requestLogin1(String userName, String password, String passwordType,String registrationId, String token, BaseObserver<LoginBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        //map.put(SecurityConstants.Parameter.GESTUREPWD, password);
        if (TextUtils.equals(passwordType, SecurityConstants.LoginType.FingerprintType)) {
            map.put(SecurityConstants.Parameter.BIOPWD, password);
        } else if (TextUtils.equals(passwordType, SecurityConstants.LoginType.GestureType)) {
            map.put(SecurityConstants.Parameter.GESTUREPWD, password);
        } else {
            map.put(SecurityConstants.Parameter.PASSWORD, password);
        }
        map.put("mobileNo", userName);
        map.put(SecurityConstants.Parameter.REGISTRATIONID, registrationId);
        map.put(SecurityConstants.Parameter.DEVICETOKEN, token);
        map.put("passwordType", passwordType);
        map.put("mobileAreaCode", "263");
        observe(mSecurityService.requestLogin(map)).subscribe(baseObserver);
    }

    /**
     * 获取授权码列表
     *
     * @param pagerNum     页数
     * @param pagerSize    角标
     * @param baseObserver 回调
     */
    public void requestAuthCodeList(String userId, int pagerNum, int pagerSize, BaseObserver<AuthorizationListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.PAGENUM, String.valueOf(pagerNum));
        map.put(SecurityConstants.Parameter.PAGESIZE, String.valueOf(pagerSize));
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestAuthCodeList(map)).subscribe(baseObserver);
    }

    /**
     * 获取授权码
     *
     * @param baseObserver 回调
     */
    public void requestAuthCode(String userId, String mobile, BaseObserver<AuthorizationBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.MOBILE, mobile);
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestAuthCode(map)).subscribe(baseObserver);
    }

    /**
     * 查询设备
     *
     * @param baseObserver 回调
     */
    public void requestQueryDevice(String userId, BaseObserver<DeviceBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestQueryDevice(map)).subscribe(baseObserver);
    }

    /**
     * 切换主设备
     *
     * @param baseObserver 回调
     */
    public void requestChangeMasterDevice(String userId, String tempDeviceId, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.TEMPDEVICEID, tempDeviceId);
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestChangeMasterDevice(map)).subscribe(baseObserver);
    }

    /**
     * 查询SDK信息
     *
     * @param baseObserver 回调
     */
    public void requestXStoreInfo(String userId, BaseObserver<SdkInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.SDKNAME, "xstore");
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestXStoreInfo(map)).subscribe(baseObserver);
    }

    /**
     * 同意协议
     */
    public void agreeCondition(String mobile, String title, String versionNo, BaseObserver<Object> baseObserver) {
        CommonModel.agreeCondition(mobile, title, versionNo, baseObserver);
    }

    /**
     * 账户激活
     */
    public void activeAccount(String userId, String currency, String pwd, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.USERID, userId);
        map.put(SecurityConstants.Parameter.PAYMENTPASSWORD, pwd);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mSecurityService.activeAccount(map)).subscribe(baseObserver);
    }

    /**
     * 登陆/手势密码验证
     * pwdType 密码类型,1-密码  3-手势
     */
    public void pwdVerify(String userId, String pwd, String pwdType, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.USERID, userId);
        map.put(SecurityConstants.Parameter.PASSWORD, pwd);
        map.put(SecurityConstants.Parameter.PASSWORDTYPE, pwdType);
        observe(mSecurityService.pwdVerify(map)).subscribe(baseObserver);
    }

    /**
     * 设置手势密码
     * gesturePwd 手势密码
     */
    public void setGesturePwd(String userId, String gesturePwd, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.USERID, userId);
        map.put(SecurityConstants.Parameter.GESTUREPASSWORD, gesturePwd);
        observe(mSecurityService.setGesturePwd(map)).subscribe(baseObserver);
    }

    /**
     * 修改手势密码
     * gesturePwd 手势密码
     */
    public void modifyGesturePwd(String userId, String gesturePwd, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.USERID, userId);
        map.put(SecurityConstants.Parameter.GESTUREPASSWORD, gesturePwd);
        observe(mSecurityService.modifyGesturePwd(map)).subscribe(baseObserver);
    }

    /**
     * 关闭手势密码
     */
    public void closeGesturePwd(String userId, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.closeGesturePwd(map)).subscribe(baseObserver);
    }

    /**
     * 请求退出接口
     *
     * @param baseObserver 回调
     */
    public void requestLogout(String userId, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(SecurityConstants.Parameter.USERID, userId);
        observe(mSecurityService.requestLogout(map)).subscribe(baseObserver);
    }

    public void getOpenNoPinList(BaseObserver<OpenNoPinListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        observe(mSecurityService.getOpenNoPinList(map)).subscribe(baseObserver);
    }

    public void getCurrencyNoPinLimit(String currency, BaseObserver<CurrencyLimitListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.DICT_CODE, currency.toLowerCase() + "_password_less_amt_limit");
        observe(mSecurityService.getCurrencyNoPinLimit(map)).subscribe(baseObserver);
    }

    public void requestOpenNoPin(String currency, String amt, String pin, BaseObserver<CurrencyLimitListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        map.put(SecurityConstants.Parameter.AMT_LIMIT, amt);
        map.put(CommonConstants.Parameter.PIN, pin);
        observe(mSecurityService.requestOpenNoPin(map)).subscribe(baseObserver);
    }

    public void requestSetNoPinMoney(String currency, String amt, String pin, BaseObserver<CurrencyLimitListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        map.put(SecurityConstants.Parameter.AMT_LIMIT, amt);
        map.put(CommonConstants.Parameter.PIN, pin);
        observe(mSecurityService.requestSetNoPinMoney(map)).subscribe(baseObserver);
    }

    public void requestCloseNoPin(String currency, String pin, BaseObserver<CurrencyLimitListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        map.put(CommonConstants.Parameter.PIN, pin);
        observe(mSecurityService.requestCloseNoPin(map)).subscribe(baseObserver);
    }

    public void requestAutoDebit(String signToken,
                                 String pin,
                                 String conditionVersionNo,
                                 String conditionTitle,
                                 BaseObserver<AutoDebitResultBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.Sign_Token, signToken);
        map.put(CommonConstants.Parameter.PIN, pin);
        map.put(CommonConstants.Parameter.Condition_VersionNo, conditionVersionNo);
        map.put(CommonConstants.Parameter.Condition_Title, conditionTitle);
        map.put(CommonConstants.Parameter.Condition_Type, "20");
        observe(mSecurityService.requestAutoDebit(map)).subscribe(baseObserver);
    }

    public void getSignInfo(String signToken, BaseObserver<AutoDebitInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.Sign_Token, signToken);
        observe(mSecurityService.getSignInfo(map)).subscribe(baseObserver);
    }

    public void requestCloseAutoDebit(String signNo, BaseObserver<AutoDebitResultBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.Sign_NO, signNo);
        observe(mSecurityService.requestCloseAutoDebit(map)).subscribe(baseObserver);
    }

    public void requestSortList(BaseObserver<PayAccountListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        observe(mSecurityService.requestSortList(map)).subscribe(baseObserver);
    }

    public void requestSortConfig(PayAccountListBean.RequestUseCardBean bean,BaseObserver<Object> baseObserver) {
        observe(mSecurityService.requestSortConfig(bean)).subscribe(baseObserver);
    }

    public void getSignList(BaseObserver<SignListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        observe(mSecurityService.getSignList(map)).subscribe(baseObserver);
    }

    public void requestUpgradeJunior(UpgradeInfoBean bean, BaseObserver<Object> baseObserver) {
        observe(mSecurityService.requestUpgradeJunior(bean)).subscribe(baseObserver);
    }

    /**
     * 请求注册接口
     */
    public void requestRegistration1(RegisterInfoBean bean, ConditionBean conditionBean, BaseObserver<RegisterResultBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.RegisterParameter.AREACODE, bean.getAreaCode());
        map.put(CommonConstants.RegisterParameter.MOBILE, bean.getMobile());
        map.put(CommonConstants.RegisterParameter.FIRSTNAME, bean.getFirstName());
        if (!TextUtils.isEmpty(bean.getMiddleName())) {
            map.put(CommonConstants.RegisterParameter.MIDDLENAME, bean.getMiddleName());
        }
        map.put(CommonConstants.RegisterParameter.LASTNAME, bean.getLastName());
        map.put(CommonConstants.RegisterParameter.GENDER, bean.getGender());
        map.put(CommonConstants.RegisterParameter.BIRTHDAY, bean.getDateOfBirth());
        map.put(CommonConstants.RegisterParameter.IDTYPE, bean.getIdType());
        map.put(CommonConstants.RegisterParameter.IDNUMBER, bean.getIdNumber());
        map.put(CommonConstants.RegisterParameter.CITYCODE, bean.getCityCode());
        map.put(CommonConstants.RegisterParameter.ADDRESS, bean.getAddress());
        map.put(CommonConstants.RegisterParameter.EMAIL, bean.getEmail());
        map.put(CommonConstants.RegisterParameter.PASSWORD, bean.getPassword());
        map.put(CommonConstants.RegisterParameter.CONDITIONVERSIONNO, conditionBean.getConditionVersionNo());
        map.put(CommonConstants.RegisterParameter.CONDITIONTITLE, conditionBean.getConditionTitle());
        map.put(CommonConstants.RegisterParameter.CONDITIONTYPE, "01");

        List<String> idPhotoUrls = bean.getIdPhotos();//最多3张
        List<String> facePhotos = bean.getFacePhotos();//最多2张
        // 👇 设置人脸照片（最多2张）
        if (facePhotos != null && !facePhotos.isEmpty()) {
            if (facePhotos.size() > 0 && facePhotos.get(0) != null) {
                map.put("customerFacialPhotoOne", facePhotos.get(0));
            }
            if (facePhotos.size() > 1 && facePhotos.get(1) != null) {
                map.put("customerFacialPhotoTwo", facePhotos.get(1));
            }
        }

        // 👇 设置身份证照片（最多3张）
        if (idPhotoUrls != null && !idPhotoUrls.isEmpty()) {
            if (idPhotoUrls.size() > 0 && idPhotoUrls.get(0) != null) {
                map.put("customerIdPhotoOne", idPhotoUrls.get(0));
            }
            if (idPhotoUrls.size() > 1 && idPhotoUrls.get(1) != null) {
                map.put("customerIdPhotoTwo", idPhotoUrls.get(1));
            }
            if (idPhotoUrls.size() > 2 && idPhotoUrls.get(2) != null) {
                map.put("customerIdPhotoThree", idPhotoUrls.get(2));
            }
        }

        observe(mSecurityService.requestRegistration(map)).subscribe(baseObserver);
    }

    public void requestJuniorRegistration(RegisterInfoBean bean, ConditionBean conditionBean, BaseObserver<RegisterResultBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.RegisterParameter.RELATION_SHIP, bean.getRelationShip() + "");
        map.put(CommonConstants.RegisterParameter.AREACODE, bean.getAreaCode());
        map.put(CommonConstants.RegisterParameter.MOBILE, bean.getMobile());
        map.put(CommonConstants.RegisterParameter.FIRSTNAME, bean.getFirstName());
        if (!TextUtils.isEmpty(bean.getMiddleName())) {
            map.put(CommonConstants.RegisterParameter.MIDDLENAME, bean.getMiddleName());
        }
        map.put(CommonConstants.RegisterParameter.LASTNAME, bean.getLastName());
        map.put(CommonConstants.RegisterParameter.GENDER, bean.getGender());
        map.put(CommonConstants.RegisterParameter.BIRTHDAY, bean.getDateOfBirth());
        map.put(CommonConstants.RegisterParameter.IDTYPE, bean.getIdType());
        map.put(CommonConstants.RegisterParameter.IDNUMBER, bean.getIdNumber());
        map.put(CommonConstants.RegisterParameter.CITYCODE, bean.getCityCode());
        map.put(CommonConstants.RegisterParameter.ADDRESS, bean.getAddress());
        map.put(CommonConstants.Parameter.CHECK_TOKEN, bean.getCheckToken());
        map.put(CommonConstants.Parameter.PIN, bean.getPassword());

        map.put(CommonConstants.RegisterParameter.CONDITIONVERSIONNO, conditionBean.getConditionVersionNo());
        map.put(CommonConstants.RegisterParameter.CONDITIONTITLE, conditionBean.getConditionTitle());
        map.put(CommonConstants.RegisterParameter.CONDITIONTYPE, conditionBean.getConditionType());
        observe(mSecurityService.requestJuniorRegistration(map)).subscribe(baseObserver);
    }

}
