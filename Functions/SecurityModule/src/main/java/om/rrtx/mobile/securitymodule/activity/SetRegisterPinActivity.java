package om.rrtx.mobile.securitymodule.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.text.method.PasswordTransformationMethod;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean;
import om.rrtx.mobile.rrtxcommon1.bean.RegisterInfoBean;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.FormatCheck;
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;
import om.rrtx.mobile.rrtxcommon1.widget.VerificationCodeEditText;
import om.rrtx.mobile.securitymodule.R;
import om.rrtx.mobile.securitymodule.R2;
import om.rrtx.mobile.securitymodule.bean.SendMessageBean;
import om.rrtx.mobile.securitymodule.dialog.KeyPadFragment;
import om.rrtx.mobile.securitymodule.presenter.PinPaymentPresenter;
import om.rrtx.mobile.securitymodule.view.PinPaymentView;
import om.rrtx.mobile.securitymodule.widget.KeyPadView;

/**
 * <AUTHOR>
 * 修改Pin页面,这里有三个页面需要跳转
 *
 * 亲子账户注册3
 * 常规账户注册5
 *
 */
@Route(path = ARouterPath.SecurityPath.SetRegisterPinActivity)
public class SetRegisterPinActivity extends BaseSuperActivity<PinPaymentView, PinPaymentPresenter>
        implements VerificationCodeEditText.VerificationCallBack,
        KeyPadView.KeyPadCallBack, PinPaymentView, KeyPadFragment.KeyPadFragmentBack {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.hintTv)
    TextView mHintTv;
    @BindView(R2.id.pin_format_tv)
    TextView mPinFormat;
    @BindView(R2.id.vce)
    VerificationCodeEditText mVce;
    @BindView(R2.id.register_con)
    ConstraintLayout register_con;
    @BindView(R2.id.tv_cur_step)
    TextView tv_cur_step;
    @BindView(R2.id.tv_all_step)
    TextView tv_all_step;
    @BindView(R2.id.tv_cur_page)
    TextView tv_cur_page;
    private String secretWord;
    private String mFlag;
    private KeyPadFragment mKeyPadFragment;
    private String mPinStrFirst;
    private String mJumpFlag;
    private String mOldPin;

    /**
     * 输入错误次数
     */
    private int errCount = 0;


    /**
     * 跳转设置Pin码的方法
     *
     * @param context  上下文
     * @param flag     flag标识
     *                 authentication 验证
     *                 setFirst 第一次设置
     *                 setSecond 第二次设置
     * @param oldPin   老密码
     * @param jumpFlag 跳转来的页面标识
     *                 psdManagerJump 密码管理页面
     *                 psdLogin 设置密码页面
     *                 homeJump 首页
     *                 loginJump 登录
     *                 forgetJump 忘记pin
     */
    public static void jumpPinPayment(Context context, String flag, String oldPin, String jumpFlag) {
        Intent intent = new Intent(context, SetRegisterPinActivity.class);
        intent.putExtra(BaseConstants.Transmit.PINPAYMENT, flag);
        intent.putExtra(BaseConstants.Transmit.JUMPFLAG, jumpFlag);
        intent.putExtra(BaseConstants.Transmit.OLDPINSTR, oldPin);
        context.startActivity(intent);
    }

    /**
     * 跳转设置Pin码的方法
     *
     * @param context  上下文
     * @param flag     flag标识
     *                 authentication 验证
     *                 setFirst 第一次设置
     *                 setSecond 第二次设置
     * @param oldPin   老密码
     * @param pinStr   pin密码
     * @param jumpFlag 跳转来的页面标识
     *                 psdManagerJump 密码管理页面
     *                 psdLogin 设置密码页面
     *                 homeJump 首页
     *                 loginJump 登录
     *                 forgetJump 忘记pin
     */
    public static void jumpPinPayment(Context context, String flag, String oldPin, String pinStr, String jumpFlag, String secretWord) {
        Intent intent = new Intent(context, SetRegisterPinActivity.class);
        intent.putExtra(BaseConstants.Transmit.PINPAYMENT, flag);
        intent.putExtra(BaseConstants.Transmit.JUMPFLAG, jumpFlag);
        intent.putExtra(BaseConstants.Transmit.PINSTR, pinStr);
        intent.putExtra(BaseConstants.Transmit.OLDPINSTR, oldPin);
        intent.putExtra(BaseConstants.Transmit.SECRETWORD, secretWord);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        secretWord = getIntent().getStringExtra(BaseConstants.Transmit.SECRETWORD);
        mFlag = getIntent().getStringExtra(BaseConstants.Transmit.PINPAYMENT);
        mPinStrFirst = getIntent().getStringExtra(BaseConstants.Transmit.PINSTR);
        mJumpFlag = getIntent().getStringExtra(BaseConstants.Transmit.JUMPFLAG);
        mOldPin = getIntent().getStringExtra(BaseConstants.Transmit.OLDPINSTR);
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.security_register_pin;
    }

    @Override
    protected PinPaymentPresenter createPresenter() {
        return new PinPaymentPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.security_ic_back);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));

        //让EditText失去焦点，然后获取点击事件
        //mVce.setFocusable(false);
        mVce.setIsPassword(true);
        mVce.setTransformationMethod(PasswordTransformationMethod.getInstance());
    }

    @Override
    public void initDate() {
        super.initDate();
        switch (mFlag) {
            case BaseConstants.Check.SETFIRST:
                initOneView();
                break;
            case BaseConstants.Check.SETSECOND:
                initTwoView();
                break;
            default:
                initOtherView();
                break;
        }

    }

    private void initOtherView() {
        mHintTv.setVisibility(View.GONE);
        mPinFormat.setVisibility(View.VISIBLE);
        mPinFormat.setText(R.string.enter_old_PIN);
        switch (mJumpFlag) {
            case BaseConstants.JumpFlag.Register_Account:
                //mHintTv.setText(R.string.enter_old_PIN);
                mTitleTv.setText(R.string.register_title_user_registration);
                register_con.setVisibility(View.VISIBLE);
                tv_cur_step.setText(getString(R.string.setpin));
                tv_cur_page.setText("5");
                tv_all_step.setText("/6");
                mVce.setCursorVisible(true);
                mVce.requestFocus();
                break;
            case BaseConstants.JumpFlag.Register_junior_Account:
                //mHintTv.setText(R.string.enter_old_PIN);
                mTitleTv.setText(R.string.register_junior);
                register_con.setVisibility(View.VISIBLE);
                tv_cur_step.setText(getString(R.string.set_juniorpin));
                tv_cur_page.setText("3");
                tv_all_step.setText("/5");
                mVce.setCursorVisible(true);
                mVce.requestFocus();
                break;
        }
    }

    private void initTwoView() {
        mHintTv.setVisibility(View.GONE);
        mPinFormat.setVisibility(View.VISIBLE);
        mPinFormat.setText(R.string.enter_the_please);
        switch (mJumpFlag) {
            case BaseConstants.JumpFlag.Register_Account:
                //mHintTv.setText(R.string.re_enter_new_PIN);
                mPinFormat.setText(R.string.re_enter_new_PIN);
                mTitleTv.setText(R.string.register_title_user_registration);
                register_con.setVisibility(View.VISIBLE);
                tv_cur_step.setText(getString(R.string.setpin));
                tv_cur_page.setText("5");
                tv_all_step.setText("/6");
                mVce.setCursorVisible(true);
                mVce.requestFocus();
                break;
            case BaseConstants.JumpFlag.Register_junior_Account:
                //mHintTv.setText(R.string.re_enter_new_PIN);
                mTitleTv.setText(R.string.register_junior);
                register_con.setVisibility(View.VISIBLE);
                tv_cur_step.setText(getString(R.string.confirm_juniorpin));
                tv_cur_page.setText("4");
                tv_all_step.setText("/5");
                mVce.setCursorVisible(true);
                mVce.requestFocus();
                break;
        }
    }

    private void initOneView() {
        mHintTv.setVisibility(View.GONE);
        mPinFormat.setVisibility(View.VISIBLE);
        mPinFormat.setText(R.string.input_pin_format);
        switch (mJumpFlag) {
            case BaseConstants.JumpFlag.Register_Account:
                //mHintTv.setText(R.string.enter_new_PIN);
                //mPinFormat.setVisibility(View.VISIBLE);
                //mPinFormat.setText(R.string.input_pin_format);
                mTitleTv.setText(R.string.register_title_user_registration);
                register_con.setVisibility(View.VISIBLE);
                tv_cur_step.setText(getString(R.string.setpin));
                tv_cur_page.setText("5");
                tv_all_step.setText("/6");
                mVce.setCursorVisible(true);
                mVce.requestFocus();
                break;
            case BaseConstants.JumpFlag.Register_junior_Account:
                //mHintTv.setText(R.string.enter_new_PIN);
                //mPinFormat.setVisibility(View.VISIBLE);
                //mPinFormat.setText(R.string.input_pin_format);
                mTitleTv.setText(R.string.register_junior);
                register_con.setVisibility(View.VISIBLE);
                tv_cur_step.setText(getString(R.string.set_juniorpin));
                tv_cur_page.setText("3");
                tv_all_step.setText("/5");
                mVce.setCursorVisible(true);
                mVce.requestFocus();
                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        mVce.setText("");
        mVce.showKeyBoard();
    }

    @Override
    public void initListener() {
        super.initListener();
        mBackIv.setOnClickListener(mPinPaymentCustom);
        mVce.setVerificationCallBack(this);
    }

    private CustomClickListener mPinPaymentCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.backIv) {
                finish();
            }
        }
    };

    /**
     * 下一步操作
     */
    private void nextClick() {
        String pin = mVce.getText().toString();
        switch (mFlag) {
            case BaseConstants.Check.SETFIRST:
                handleOneClick(pin);
                break;
            case BaseConstants.Check.SETSECOND:
                handleTwoClick(pin);
                break;
            default:
                handleOtherClick(pin);
                break;
        }

    }

    private void handleOtherClick(String pin) {
        mPresenter.requestValidatePin(pin);
    }

    private void handleTwoClick(String pin) {
        if (TextUtils.equals(mFlag, BaseConstants.Check.SETSECOND)) {
            //第二次设置Pin密码
            if (!TextUtils.isEmpty(mPinStrFirst)) {
                String paymentPsd = mVce.getText().toString();
                if (!TextUtils.equals(paymentPsd, mPinStrFirst)) {
                    errCount++;
                    mVce.setText("");
                    ToastUtil.show(SetRegisterPinActivity.this, getString(R.string.input_pin_inconsistent_hint));
                    //这里错误六次的话 直接退到最初的页面
                    if (errCount >= 6) {
                        finish();
                    }
                    return;
                }

                switch (mJumpFlag) {
                    case BaseConstants.JumpFlag.Register_junior_Account:
                        // 注册亲子
                        String conditionJson = SharedPreferencesUtils.getParam(this, BaseConstants.SaveParameter.CONDITION_INFO, "").toString();
                        ConditionBean conditionBean = new Gson().fromJson(conditionJson, ConditionBean.class);

                        String registerJson = SharedPreferencesUtils.getParam(this, BaseConstants.SaveParameter.REGISTER_INFO, "").toString();
                        RegisterInfoBean registrationBean = new Gson().fromJson(registerJson, RegisterInfoBean.class);
                        registrationBean.setPassword(pin);
                        mPresenter.requestJuniorRegistration(registrationBean, conditionBean);
                        break;
                    case BaseConstants.JumpFlag.Register_Account:
                        // 注册常规
                        String conditionJson11 = SharedPreferencesUtils.getParam(this, BaseConstants.SaveParameter.CONDITION_INFO, "").toString();
                        ConditionBean conditionBean11 = new Gson().fromJson(conditionJson11, ConditionBean.class);

                        String registerJson11 = SharedPreferencesUtils.getParam(this, BaseConstants.SaveParameter.REGISTER_INFO, "").toString();
                        RegisterInfoBean registrationBean11 = new Gson().fromJson(registerJson11, RegisterInfoBean.class);
                        registrationBean11.setPassword(pin);
                        mPresenter.requestRegistration(registrationBean11, conditionBean11);
                        break;
                    default:
                        //修改Pin接口
                        mPresenter.requestChangePin(mOldPin, paymentPsd);
                        break;
                }

            }
        }
    }

    private void handleOneClick(String pin) {
        //第一次设置Pin密码
        String paymentPsd = mVce.getText().toString();
        if (FormatCheck.pinFromCheck(pin)) {
            if (paymentPsd.equals(mOldPin)) {
                ToastUtil.show(mContext, ResourceHelper.getString(R.string.input_new_pin_error_hint));
            } else {
                SetRegisterPinActivity.jumpPinPayment(mContext, BaseConstants.Check.SETSECOND, mOldPin, paymentPsd, mJumpFlag, secretWord);
            }
        } else {
            ToastUtil.show(mContext, ResourceHelper.getString(R.string.input_new_pin_fromat_hint));
        }
    }

    @Override
    public void showKeyBoard() {
        if (mKeyPadFragment == null) {
            mKeyPadFragment = new KeyPadFragment("1");
            mKeyPadFragment.setKeyPadFragmentBack(this);
        }
        mVce.setText("");
        mKeyPadFragment.show(getSupportFragmentManager(), "");
    }

    @Override
    public void inputCode(String code) {
        mVce.setText(code);
        if (code.length() == 4) {
            if (mKeyPadFragment != null) {
                mKeyPadFragment.dismiss();
            }
            nextClick();
        }
    }

    @Override
    public void onKeyPadPause() {
        if (mVce.length() < 4) {
            mVce.setText("");
        }
    }

    @Override
    public void validatePinSuccess() {
        String oldPin = mVce.getText().toString();
        SetRegisterPinActivity.jumpPinPayment(mContext, BaseConstants.Check.SETFIRST, oldPin, mJumpFlag);
    }

    @Override
    public void requestFail(String sResMsg) {
        mVce.setText("");
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void setPinSuccess() {
        if (!TextUtils.isEmpty(mJumpFlag)) {
            PsdSuccessActivity.jumpPsdSuccess(mContext, mJumpFlag);
        }
    }

    @Override
    public void changePinSuccess() {
        PsdSuccessActivity.jumpPsdSuccess(mContext, mJumpFlag);
    }

    @Override
    public void registerJuniorSuccess(String userStatus) {
        ARouter.getInstance()
                .build(ARouterPath.LoginPath.RegisterResultActivity)
                .withString(BaseConstants.Transmit.JUMPFLAG, mJumpFlag)
                .withString(BaseConstants.Transmit.USERSTATUS, userStatus)
                .navigation();
    }

    @Override
    public void registerSuccess(String userStatus) {
        ARouter.getInstance()
                .build(ARouterPath.LoginPath.RegisterResultActivity)
                .withString(BaseConstants.Transmit.JUMPFLAG, mJumpFlag)
                .withString(BaseConstants.Transmit.USERSTATUS, userStatus)
                .navigation();
    }
}
