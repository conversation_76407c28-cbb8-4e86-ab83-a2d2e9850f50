## For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx1024m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
#Sun Apr 05 23:45:38 CST 2020
android.enableJetifier=true
org.gradle.jvmargs=-Xmx2048M -Dkotlin.daemon.jvm.options\="-Xmx2048M"
android.useAndroidX=true
# Java 17 compatibility
org.gradle.java.home=/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home
#解决vivo无法通过usb安装的问题
android.injected.testOnly=false
android.jetifier.ignorelist=bcprov-jdk15on
# 这里管理项目中使用到的一些变量参数还是比较好的!
# 因为配置在这里的话,可以做到jenkens里面去
# appType app的打包类型 这里暂时定义几种类型!
# test 测试环境
# dev 开发环境
# uat 演示环境
# fat 验收环境
AppType=test
# 项目的包名 我觉得在这里更护的话比较合适
APPPACKNAME=zw.co.onemoney.mob
# 极光的appKey
# 本地测试->099e47d992442b2db3b971f6
# 我的测试->9c1a277f1244f07233947c11
# JPUSH_APPKEY=099e47d992442b2db3b971f6
# 谷歌地图的APIKEY
#GOOGLE_MAP_KEY=AIzaSyBJxFqNea3qfNYuOF2SKVuZuNr92SWiBoM
#new
#GOOGLE_MAP_KEY=AIzaSyCq90B7ZSvsD5t0X7YcxYgaipA34XbW1bc
GOOGLE_MAP_KEY=AIzaSyAFqvK2Wrq8YATLzylYAO6E2enAA4vjjyU
#android.enableBuildCache=false