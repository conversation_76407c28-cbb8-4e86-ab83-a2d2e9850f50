// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        //google()
        //jcenter()
        /*maven { url 'https://jitpack.io' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }*/
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.aliyun.com/nexus/content/groups/public' }
        maven {
            allowInsecureProtocol = true
            url "http://download.flutter.io" }
        maven {
            url 'https://nexus.vimbug.com/repository/maven-public/'
            credentials {
                username 'diasia'
                password 'diasia@2o22'
            }
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath 'com.jakewharton:butterknife-gradle-plugin:10.2.1'
        classpath 'com.neenbedankt.gradle.plugins:android-apt:1.8' //路由需要
        classpath 'com.google.gms:google-services:4.3.15'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.7.10"
        // NOTE: Do not place your application dependencies here; they belong
    }
}

allprojects {
    repositories {
        //google()
        //jcenter()
        /*maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
        maven { url 'https://jitpack.io' }*/
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.aliyun.com/nexus/content/groups/public' }
        maven {
            allowInsecureProtocol = true
            url "http://download.flutter.io" }
        maven {
            url 'https://nexus.vimbug.com/repository/maven-public/'
            credentials {
                username 'diasia'
                password 'diasia@2o22'
            }
        }

        flatDir {
            // 由于Library module中引用了 youkuplayer 库的 aar，在多 module 的情况下，
            // 其他的module编译会报错，所以需要在所有工程的repositories
            // 下把Library module中的libs目录添加到依赖关系中
            dirs project(':BaseModule:RrtxCommon').file('libs')
        }
    }


}

task clean(type: Delete) {
    delete rootProject.buildDir
}

ext {
    configuration = [
            applicationId    : "zw.co.onemoney.mob",
            buildToolsVersion: "30.0.2",
            compileVersion   : 34,
            minSdk           : 24,
            targetSdk        : 34,
            version_code     : 7,
            version_name     : "1.1.1",
    ]
    libraries = [
            androidx_appcompat       : "1.4.0",
            androidx_recyclerview    : "1.1.0",
            androidx_constraintlayout: "2.0.1",
            logging_interceptor      : "4.8.1",
            retrofit                 : "2.6.0",
            converter_gson           : "2.6.0",
            adapter_rxjava2          : "2.6.0",
            rxandroid                : "2.1.1",
            rxjava                   : "2.2.10",
            butterknife              : "10.2.1",
            butterknife_compiler     : "10.2.1",
            bga_qrcode_zbar          : "1.3.7",
            zxing_code               : "3.3.3",
            rxpermissions            : "0.10.2",
            logger                   : "2.2.0",
            persistent_cookie_jar    : "v1.0.1",
            banner                   : "1.4.10",
            glide                    : "4.16.0",
            glide_compiler           : "4.10.0",
            legacy_support_v4        : "1.0.0",
            immersionbar_components  : "3.0.0",
            immersionbar             : "3.0.0",
            smartRefreshLayout       : "*******",
            arouter_api              : "1.5.0",
            arouter_compiler         : "1.2.2",
            lifecycle_extensions     : "2.2.0",
            meterial                 : "1.2.1",
            circleimageview          : "3.1.0",
            hutool_all               : "5.1.0",
            multidex                 : "1.3.0",
            swipelayout              : "1.2.0@aar",
            wheelview_lib            : "1.2.2",
            stickyDecoration         : "1.5.2",

            FlycoTabLayout_Lib       : "2.1.2@aar",
            localbroadcastmanager    : "1.0.0",
            biometric                : "1.0.1",
            swipebacklayout          : "2.0.1",
            photoview                : "2.3.0",
            lottie                   : "3.4.0",
            badgeview                : "1.0.5",
            //Jetpack组件
            lifecycle_runtime        : "2.2.0",
            lifecycle_common_java8   : "2.2.0",
            lifecycle_extensions     : "2.2.0",
            lifecycle_viewmodel      : "2.2.0",
            lifecycle_livedata       : "2.2.0",
            navigation_fragment      : "2.2.2",
            navigation_ui            : "2.2.2",
    ]
}

